plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
}
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

android {
    signingConfigs {
        debug {
            storeFile file('/home/<USER>/.android/debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildToolsVersion '33.0.1'

    defaultConfig {
        compileSdkVersion 34
        applicationId "co.metode.hamim"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 18
        versionName "2.0.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        manifestPlaceholders = [
                testInstrumentationRunnerAllowed: true
        ]
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    kotlin {
        jvmToolchain(17)
    }
    lintOptions{
        checkReleaseBuilds false
    }
    namespace 'co.metode.hamim'

    buildFeatures {
        dataBinding = true
        viewBinding true
    }

    viewBinding {
        enabled = true
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.mediarouter:mediarouter:1.3.0'
    implementation 'androidx.media:media:1.6.0'
    implementation 'com.google.android.gms:play-services-cast-framework:21.0.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'com.google.code.gson:gson:2.8.9'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'org.glassfish:javax.annotation:10.0-b28'
    //noinspection GradleCompatible
    implementation 'com.android.support:design:28.0.0'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'com.google.android.exoplayer:exoplayer:2.13.3'
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    implementation platform('com.google.firebase:firebase-bom:33.3.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.android.gms:play-services-auth:20.7.0'

    //  exoplayer library
    //
    implementation 'com.google.android.exoplayer:exoplayer-core:2.13.3'
    implementation 'com.google.android.exoplayer:exoplayer-dash:2.13.3'
    implementation 'com.google.android.exoplayer:exoplayer-hls:2.13.3'
    implementation 'com.google.android.exoplayer:exoplayer-smoothstreaming:2.13.3'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.13.3'
    implementation 'com.google.android.exoplayer:extension-cast:2.13.3'

    //
    implementation 'com.intuit.sdp:sdp-android:1.0.5'
    implementation 'com.intuit.ssp:ssp-android:1.0.5'
    //
    implementation 'com.google.firebase:firebase-messaging:23.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.navigation:navigation-fragment:2.3.5'
    implementation 'androidx.navigation:navigation-ui:2.3.5'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.4.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.1'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'org.apache.commons:commons-io:1.3.2'
    implementation 'androidx.activity:activity:1.8.0'

    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    implementation 'com.android.volley:volley:1.2.0'
    androidTestImplementation 'androidx.test:runner:1.5.2'
//    youtube library
    implementation 'com.github.PierfrancescoSoffritti.android-youtube-player:chromecast-sender:10.0.5'

//    Payment Gateway
//    implementation ('com.midtrans:uikit:1.29.0') {
//        transitive = true
//        exclude group: 'com.google.android.gms'
//    }
    implementation ('com.midtrans:uikit:1.30.1'){
        transitive = true
        exclude group: 'com.google.android.gms'
    }

//    implementation 'com.midtrans:uikit:1.30.1-SANDBOX'
//    implementation 'com.midtrans:corekit:1.30.1'

    implementation 'com.koushikdutta.ion:ion:3.1.0'

    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

//    loading library
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'

    implementation 'me.saket:better-link-movement-method:2.2.0'


    // Declare the dependencies for the In-App Messaging and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-inappmessaging-display'

    implementation "com.airbnb.android:lottie:5.0.3"
    implementation 'com.github.Cutta:GifView:1.4'

//    View pager
    implementation 'androidx.viewpager2:viewpager2:1.0.0-alpha02'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    // image slider
    implementation 'com.github.smarteist:Android-Image-Slider:1.4.0'

    // okhttp
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    //Country Code Number
    implementation 'com.hbb20:ccp:2.5.0'

    //Circle Image View
    implementation 'de.hdodenhof:circleimageview:3.1.0'

    //Room
    implementation "androidx.room:room-runtime:2.6.1"
    annotationProcessor "androidx.room:room-compiler:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"

    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    //Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'

    //GMS
    implementation 'com.google.android.gms:play-services-base:18.2.0'

    //eBOOK
    implementation 'io.coil-kt:coil:2.5.0'
    implementation 'androidx.fragment:fragment-ktx:1.8.5'



    // Gson for JSON serialization/deserialization
    implementation 'com.google.code.gson:gson:2.10.1'



    implementation "androidx.room:room-runtime:2.5.0"
    implementation "androidx.room:room-ktx:2.5.0"
    kapt "androidx.room:room-compiler:2.5.0"
    implementation "com.google.dagger:hilt-android:2.44"
    kapt "com.google.dagger:hilt-android-compiler:2.44"

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'

    // Lifecycle
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
}