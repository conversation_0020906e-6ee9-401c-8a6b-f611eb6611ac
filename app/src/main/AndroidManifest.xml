<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="Missing<PERSON><PERSON>backLauncher">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <application
        android:name=".exoplayer.AdaptiveExoplayer"
        android:allowBackup="true"
        android:allowClearUserData="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_app"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_app_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.HAMIM"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup, android:usesCleartextTraffic">
        <activity
            android:name=".AudioChallenge"
            android:exported="false" />
        <activity
            android:name=".juz.ChallengeActivity"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.detail.AddReview"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.detail.ReviewFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.detail.JilidFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.detail.TentangFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.BookDetailActivity"
            android:exported="false" />

        <!-- PDF Viewer Activity -->
        <activity
            android:name=".ebook.pdfviewer.PdfViewerActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />


        <activity
            android:name=".ebook.fragment.PurchasedBooksFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.FreeBooksFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.fragment.AllBooksFragment"
            android:exported="false" />
        <activity
            android:name=".ebook.BookActivity"
            android:exported="false" />
        <activity
            android:name=".profile.EditProfile"
            android:exported="false" />
        <activity
            android:name=".ziswaf.RiwayatDoa"
            android:exported="false" />
        <activity
            android:name=".kursus.ui.PrivatActivity"
            android:exported="false" />
        <activity
            android:name=".kursus.ui.KursusActivity"
            android:exported="false" />
        <activity
            android:name=".onBoardingRegister.ui.OnboardingRegisterActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".allMenuBeranda.CekBacaan.RiwayatCekBacaan"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.murojaah.PlayMurojaahFinal"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".allMenuBeranda.murojaah.DetailSambungSurat"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.murojaah.SambungAyat"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.murojaah.GabunganSurat"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.murojaah.PilihanMurojaah"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.artikel.Artikel"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.murojaah.Murojaah"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.CekBacaan.DetailCekBacaan"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.CekBacaan.RekamBacaan"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.CekBacaan.DaftarSuratCekBacaan"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.CekBacaan.CekBacaan"
            android:exported="true" />
        <activity
            android:name=".test.TestDownload"
            android:exported="true" />
        <activity
            android:name=".test.TestBase64"
            android:exported="true" />
        <activity
            android:name=".payment.redirect.RedirectWa"
            android:exported="true" />
        <activity
            android:name=".payment.redirect.RedirectVa"
            android:exported="true" />
        <activity
            android:name=".payment.PembayaranVa"
            android:exported="true" />
        <activity
            android:name=".payment.PaymentMethod"
            android:exported="true" />
        <activity
            android:name=".payment.Checkout"
            android:exported="true" />
        <activity
            android:name=".SplashMasuk"
            android:exported="true" />
        <activity
            android:name=".qrcode.CaptureAct"
            android:exported="true"
            android:screenOrientation="portrait"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme" />
        <activity android:name=".qrcode.QRCode" />
        <activity
            android:name=".event.PayEventNew"
            android:exported="true" />
        <activity
            android:name=".ecourse.PayEcourse"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.RiwayatLaporan"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.HubungiKami"
            android:exported="true" />
        <activity
            android:name=".ecourse.DeskEcourse"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.Faq"
            android:exported="true" />
        <activity
            android:name=".test.TestCast"
            android:exported="true" />

        <meta-data
            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
            android:exported="true"
            android:value="com.google.android.exoplayer2.ext.cast.DefaultCastOptionsProvider" />

        <activity
            android:name=".onBoarding.first.onBoardingFirst"
            android:exported="true" />
        <activity
            android:name=".qiblat.CompassActivity"
            android:exported="true" />
        <activity
            android:name=".mushaf.mushafnew.DetailSuratNewMushaf"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true"
            android:hardwareAccelerated="false" />
        <activity
            android:name=".mushaf.mushafnew.MushafNew"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".Test"
            android:exported="true"
            android:label="@string/title_activity_test"
            android:theme="@style/Theme.HAMIM.NoActionBar" />
        <activity
            android:name=".ziswaf.RiwayatZiswaf"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".ziswaf.InfaqZiswaf"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".ziswaf.DetailZiswaf"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".ziswaf.Ziswaf"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".event.DetailEventku"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="api.whatsapp.com"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".event.payEvent.PayEvent"
            android:exported="true" />
        <activity
            android:name=".event.payEvent.paymentEvent"
            android:exported="true" />
        <activity
            android:name=".event.DetailEvent"
            android:exported="true" />
        <activity
            android:name=".event.Event"
            android:exported="true" />
        <activity
            android:name=".CompleteTransaksi"
            android:exported="true" />
        <activity
            android:name=".fragment.fragmentTransaksi.selesai.RincianSelesai"
            android:exported="true" />
        <activity
            android:name=".fragment.fragmentTransaksi.belumbayar.RincianBelumBayar"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.Transaksi"
            android:exported="true" />
        <activity
            android:name=".onBoardingSetoran.onBoardingSetoran"
            android:exported="true" />
        <activity
            android:name=".allMenuBeranda.Setoran"
            android:exported="true" />
        <activity
            android:name=".Home"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".daftar_berita.DetailBerita"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".topup.topUp"
            android:exported="true" />
        <activity
            android:name=".paymentHamim.PayHamim"
            android:exported="true" />
        <activity
            android:name=".PilihanPoi"
            android:exported="true" />
        <activity
            android:name=".LoadingScreen"
            android:exported="true" />
        <activity
            android:name=".LogoutId"
            android:exported="true" />
        <activity
            android:name=".NewPassword"
            android:exported="true" />
        <activity
            android:name=".VerifikasiLupaPw"
            android:exported="true" />
        <activity
            android:name=".LupaPassword"
            android:exported="true" />
        <activity
            android:name=".exoplayer.HomeActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".exoplayer.DownloadActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".exoplayer.OnlinePlayerActivity"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".exoplayer.OfflinePlayerActivity"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".Mushaf"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="unspecified" />
        <activity
            android:name=".Success"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".SplashScreen"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".DataPribadi"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Bantuan"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".juz.JuzActivity"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Aktivasi_ecourse"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".EcoursePlay"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".DetailEcourse"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Profile"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Ecourse"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Hamim"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Kode_hamim"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".PlayVideo"
            android:configChanges="orientation|screenSize|layoutDirection"
            android:exported="true" />
        <activity
            android:name=".Detail_surat_video"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Video"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Detail_surat"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Play"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Audio"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".Register"
            android:exported="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".MainActivity"
            android:exported="true" />

        <service
            android:name=".exoplayer.DemoDownloadService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />
            </intent-filter>
        </service>
        <service
            android:name="com.google.android.exoplayer2.scheduler.PlatformScheduler$PlatformSchedulerService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name=".FirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Background audio service -->
        <service
            android:name=".play.MediaPlayerService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <!-- FileProvider for sharing PDFs -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>