package co.metode.hamim.ebook.model

import co.metode.hamim.ebook.Book
import com.google.gson.annotations.SerializedName

/**
 * Data class representing a book from the ruangbaca API
 */
data class EbookItem(
    @SerializedName("id_buku")
    val idBuku: Int,

    @SerializedName("id_program_app")
    val idProgramApp: String,

    @SerializedName("nama_buku")
    val namaBuku: String,

    @SerializedName("buku")
    val buku: String,

    @SerializedName("cover_buku")
    val coverBuku: String,

    @SerializedName("harga")
    val harga: String,

    @SerializedName("type")
    val type: String,

    @SerializedName("cover_url")
    val coverUrl: String,

    @SerializedName("download_url")
    val downloadUrl: String,

    @SerializedName("view_url")
    val viewUrl: String
) {
    /**
     * Check if the book is free
     */
    val isFree: Boolean
        get() = type.equals("free", ignoreCase = true)

    /**
     * Check if the book is premium
     */
    val isPremium: Boolean
        get() = type.equals("premium", ignoreCase = true)

    /**
     * Convert to Book model for UI
     */
    fun toBook(): Book {
        return Book(
            id = idBuku.toString(),
            title = namaBuku,
            author = "", // API doesn't provide author, can be updated later
            year = 0,    // API doesn't provide year, can be updated later
            coverUrl = coverUrl,
            isPurchased = false, // This will need to be determined by user's purchase history
            isFree = isFree
        )
    }
}
