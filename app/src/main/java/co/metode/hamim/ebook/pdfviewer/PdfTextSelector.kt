package co.metode.hamim.ebook.pdfviewer

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.util.Log
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.text.PDFTextStripper
import com.tom_roush.pdfbox.text.TextPosition
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.ArrayList

/**
 * Class for handling text selection in PDF documents
 */
class PdfTextSelector(private val document: PDDocument) {

    // Text positions for the current page
    private var textPositions = mutableListOf<TextPositionInfo>()

    // Current page index
    private var currentPageIndex = 0

    // Selection state
    private var selectionStartIndex = -1
    private var selectionEndIndex = -1
    private var selectedText = ""

    /**
     * Load text positions for a specific page
     */
    suspend fun loadTextPositions(pageIndex: Int) = withContext(Dispatchers.IO) {
        try {
            currentPageIndex = pageIndex
            textPositions.clear()
            selectionStartIndex = -1
            selectionEndIndex = -1
            selectedText = ""

            // Create a custom text stripper to collect text positions
            val textPositionCollector = object : PDFTextStripper() {
                private val collectedPositions = mutableListOf<TextPositionInfo>()

                init {
                    // Best practice dari PDFBox: urutkan teks berdasarkan posisi
                    setSortByPosition(true)

                    // Best practice dari PDFBox: atur parameter untuk ekstraksi teks yang lebih baik
                    setLineSeparator("\n")
                    setWordSeparator(" ")
                    setSuppressDuplicateOverlappingText(true)
                }

                override fun writeString(text: String, textPositions: List<TextPosition>) {
                    // Process each character and its position
                    for (i in textPositions.indices) {
                        val position = textPositions[i]
                        val char = text[i].toString()

                        // Check if we need to insert a space
                        if (i > 0 && collectedPositions.isNotEmpty() && !collectedPositions.last().endOfLine) {
                            val prevPosition = textPositions[i-1]

                            // Calculate the gap between this character and the previous one
                            val gap = position.x - (prevPosition.x + prevPosition.width)

                            // If the gap is significant (more than half the width of a space), insert a space
                            // This helps with detecting spaces that aren't explicitly in the PDF
                            // Best practice dari PDFBox: gunakan 0.3 kali lebar karakter untuk mendeteksi spasi
                            if (gap > position.width * 0.3f && !char.contains(" ") &&
                                !collectedPositions.last().char.contains(" ")) {

                                // Create a space character at the appropriate position
                                val spaceInfo = TextPositionInfo(
                                    char = " ",
                                    x = prevPosition.x + prevPosition.width,
                                    y = position.y,
                                    width = gap,
                                    height = position.height,
                                    endOfLine = false,
                                    fontSizeInPt = position.fontSizeInPt
                                )

                                collectedPositions.add(spaceInfo)

                                // Log untuk debugging
                                android.util.Log.d("PdfTextSelector", "Added implicit space at x=${spaceInfo.x}, width=${spaceInfo.width}")
                            }
                        }

                        // Skip whitespace at beginning of lines
                        if (char.isNotEmpty() && char[0].isWhitespace() && collectedPositions.isNotEmpty() &&
                            collectedPositions.last().endOfLine) {
                            continue
                        }

                        // Create text position info
                        val info = TextPositionInfo(
                            char = char,
                            x = position.x,
                            y = position.y,
                            width = position.width,
                            height = position.height,
                            endOfLine = false,
                            fontSizeInPt = position.fontSizeInPt
                        )

                        collectedPositions.add(info)
                    }

                    // Mark the last character as end of line
                    if (collectedPositions.isNotEmpty()) {
                        collectedPositions.last().endOfLine = true
                    }

                    // Add an explicit space at the end of each line to ensure paragraphs are separated
                    if (collectedPositions.isNotEmpty() && !collectedPositions.last().char.contains(" ")) {
                        val lastPos = collectedPositions.last()
                        val spaceInfo = TextPositionInfo(
                            char = " ",
                            x = lastPos.x + lastPos.width,
                            y = lastPos.y,
                            width = lastPos.width * 0.5f,
                            height = lastPos.height,
                            endOfLine = true,
                            fontSizeInPt = lastPos.fontSizeInPt
                        )
                        collectedPositions.add(spaceInfo)
                    }
                }

                fun getCollectedPositions(): List<TextPositionInfo> {
                    return collectedPositions
                }
            }

            // Set page range
            textPositionCollector.startPage = pageIndex + 1
            textPositionCollector.endPage = pageIndex + 1

            // Extract text (this will populate the positions list)
            textPositionCollector.getText(document)

            // Store text positions
            <EMAIL> = textPositionCollector.getCollectedPositions().toMutableList()

            Log.d(TAG, "Loaded ${<EMAIL>} text positions for page ${pageIndex + 1}")
        } catch (e: IOException) {
            Log.e(TAG, "Error loading text positions", e)
        }
    }

    /**
     * Find the nearest text position to the given point
     */
    fun findNearestPosition(x: Float, y: Float, scale: Float): Int {
        if (textPositions.isEmpty()) return -1

        // Log untuk debugging
        android.util.Log.d("PdfTextSelector", "Finding nearest position to x=$x, y=$y")

        var nearestIndex = -1
        var minDistance = Float.MAX_VALUE

        // Best practice dari PDFBox: berikan bobot lebih pada jarak vertikal
        // Ini membantu seleksi teks yang lebih akurat pada dokumen multi-kolom
        val verticalWeight = 2.0f

        for (i in textPositions.indices) {
            val position = textPositions[i]

            // Calculate distance to this position
            val posX = position.x * scale
            val posY = position.y * scale

            // Hitung jarak horizontal dan vertikal secara terpisah
            val horizontalDistance = Math.abs(posX - x)
            val verticalDistance = Math.abs(posY - y) * verticalWeight

            // Gunakan jarak Euclidean dengan bobot
            val distance = Math.sqrt(
                Math.pow(horizontalDistance.toDouble(), 2.0) +
                Math.pow(verticalDistance.toDouble(), 2.0)
            ).toFloat()

            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = i
            }
        }

        // Log hasil untuk debugging
        if (nearestIndex >= 0) {
            val position = textPositions[nearestIndex]
            android.util.Log.d("PdfTextSelector", "Found nearest position at index=$nearestIndex, char='${position.char}', x=${position.x * scale}, y=${position.y * scale}")
        }

        return nearestIndex
    }

    /**
     * Start text selection at the given position
     */
    fun startSelection(index: Int) {
        if (index < 0 || index >= textPositions.size) {
            android.util.Log.e("PdfTextSelector", "Invalid index for startSelection: $index")
            return
        }

        android.util.Log.d("PdfTextSelector", "startSelection called with index=$index")

        selectionStartIndex = index
        selectionEndIndex = index
        updateSelectedText()

        android.util.Log.d("PdfTextSelector", "Selection indices: start=$selectionStartIndex, end=$selectionEndIndex")
        android.util.Log.d("PdfTextSelector", "Selected text: '$selectedText'")
    }

    /**
     * Update the end position of the selection
     */
    fun updateSelection(index: Int) {
        if (index < 0 || index >= textPositions.size || selectionStartIndex < 0) return

        selectionEndIndex = index
        updateSelectedText()
    }

    /**
     * Update the selected text based on current selection indices
     */
    private fun updateSelectedText() {
        if (selectionStartIndex < 0 || selectionEndIndex < 0) {
            selectedText = ""
            return
        }

        val startIdx = minOf(selectionStartIndex, selectionEndIndex)
        val endIdx = maxOf(selectionStartIndex, selectionEndIndex)

        // Log untuk debugging
        android.util.Log.d("PdfTextSelector", "Updating selected text from index $startIdx to $endIdx")

        val builder = StringBuilder()
        var lastY = -1f
        var lastX = -1f
        var lastFontSize = -1f
        var lastLineEnd = false

        for (i in startIdx..endIdx) {
            val position = textPositions[i]
            val char = position.char

            // Add space if there's a significant gap between characters on the same line
            // or if we're moving to a new line with similar Y coordinate (for right-to-left text)
            if (i > startIdx) {
                val prevPosition = textPositions[i-1]

                // Check if we're on the same line (Y position within tolerance)
                // Best practice dari PDFBox: gunakan 25% dari tinggi font untuk toleransi
                val sameLineY = Math.abs(position.y - prevPosition.y) < position.height * 0.25f

                // Check if there's a significant gap between characters
                // Best practice dari PDFBox: gunakan 0.5 kali lebar karakter untuk mendeteksi gap
                val significantGap = position.x - (prevPosition.x + prevPosition.width) > position.width * 0.5f

                // Check if we're at the start of a new line
                val newLine = !sameLineY || prevPosition.endOfLine || lastLineEnd

                // Check if font size changed significantly (might indicate a different text block)
                // Best practice dari PDFBox: gunakan 0.5 point sebagai toleransi
                val fontSizeChanged = Math.abs(position.fontSizeInPt - prevPosition.fontSizeInPt) > 0.5f

                // Add space if there's a gap or we're at a new line
                if ((sameLineY && significantGap) || newLine || fontSizeChanged) {
                    builder.append(" ")
                    android.util.Log.d("PdfTextSelector", "Added space before '${char}' (gap=$significantGap, newLine=$newLine, fontChange=$fontSizeChanged)")
                }
            }

            // Add the character
            builder.append(char)

            // Update last position
            lastY = position.y
            lastX = position.x
            lastFontSize = position.fontSizeInPt
            lastLineEnd = position.endOfLine
        }

        selectedText = builder.toString()
        android.util.Log.d("PdfTextSelector", "Selected text: '$selectedText'")
    }

    /**
     * Get the currently selected text
     */
    fun getSelectedText(): String {
        return selectedText
    }

    /**
     * Clear the current selection
     */
    fun clearSelection() {
        selectionStartIndex = -1
        selectionEndIndex = -1
        selectedText = ""
    }

    /**
     * Get character at specified position
     */
    fun getCharAt(index: Int): String {
        return if (index >= 0 && index < textPositions.size) {
            textPositions[index].char
        } else {
            ""
        }
    }

    /**
     * Get total number of text positions
     */
    fun getTextPositionsCount(): Int {
        return textPositions.size
    }

    /**
     * Get position information at the given index
     */
    fun getPositionAt(index: Int): TextPositionInfo? {
        return if (index >= 0 && index < textPositions.size) {
            textPositions[index]
        } else {
            null
        }
    }

    /**
     * Check if there is text at the given position using PDFBox approach
     * Based on PDFBox best practices for text detection
     */
    fun hasTextAtPosition(x: Float, y: Float, scale: Float): Boolean {
        if (textPositions.isEmpty()) return false

        // Konversi koordinat ke koordinat PDF
        val pdfX = x / scale
        val pdfY = y / scale

        android.util.Log.d("PdfTextSelector", "Checking for text at PDF coordinates ($pdfX, $pdfY)")

        // Toleransi untuk deteksi teks (dalam koordinat PDF)
        // PDFBox best practice: gunakan toleransi yang proporsional dengan ukuran font
        var minDistance = Float.MAX_VALUE
        var closestChar = ""
        var foundText = false

        // Cek apakah ada teks di sekitar posisi yang diberikan
        for (position in textPositions) {
            // Hitung toleransi berdasarkan ukuran font
            val fontBasedTolerance = position.height * 1.5f

            // Buat bounding box untuk posisi teks dengan toleransi yang proporsional
            val left = position.x - fontBasedTolerance
            val top = position.y - position.height - fontBasedTolerance
            val right = position.x + position.width + fontBasedTolerance
            val bottom = position.y + fontBasedTolerance

            // Cek apakah posisi berada di dalam bounding box
            if (pdfX >= left && pdfX <= right && pdfY >= top && pdfY <= bottom) {
                // Hitung jarak ke pusat karakter
                val centerX = position.x + position.width / 2
                val centerY = position.y - position.height / 2
                val distance = Math.sqrt(
                    Math.pow((centerX - pdfX).toDouble(), 2.0) +
                    Math.pow((centerY - pdfY).toDouble(), 2.0)
                ).toFloat()

                // Perbarui karakter terdekat
                if (distance < minDistance) {
                    minDistance = distance
                    closestChar = position.char
                    foundText = true
                }
            }
        }

        if (foundText) {
            android.util.Log.d("PdfTextSelector", "Found text '$closestChar' at distance $minDistance")
        } else {
            android.util.Log.d("PdfTextSelector", "No text found at position ($pdfX, $pdfY)")
        }

        return foundText
    }

    /**
     * Draw the current selection on the bitmap
     */
    fun drawSelection(bitmap: Bitmap, scale: Float): Bitmap {
        if (selectionStartIndex < 0 || selectionEndIndex < 0) return bitmap

        val startIdx = minOf(selectionStartIndex, selectionEndIndex)
        val endIdx = maxOf(selectionStartIndex, selectionEndIndex)

        // Create a mutable copy of the bitmap
        val mutableBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(mutableBitmap)

        // Paint for selection highlight
        val paint = Paint().apply {
            color = Color.parseColor("#33689DF7") // Light blue with transparency
            style = Paint.Style.FILL
        }

        // Group text positions into lines
        val lines = mutableListOf<MutableList<TextPositionInfo>>()
        var currentLine = mutableListOf<TextPositionInfo>()

        for (i in startIdx..endIdx) {
            val position = textPositions[i]
            currentLine.add(position)

            if (position.endOfLine) {
                lines.add(currentLine)
                currentLine = mutableListOf()
            }
        }

        // Add the last line if not empty
        if (currentLine.isNotEmpty()) {
            lines.add(currentLine)
        }

        // Draw each line as a rectangle
        for (line in lines) {
            if (line.isEmpty()) continue

            val firstPos = line.first()
            val lastPos = line.last()

            // Calculate rectangle for this line
            val left = firstPos.x * scale
            val top = (firstPos.y - firstPos.height) * scale
            val right = (lastPos.x + lastPos.width) * scale
            val bottom = (lastPos.y) * scale

            // Draw the selection rectangle
            canvas.drawRect(left, top, right, bottom, paint)
        }

        return mutableBitmap
    }

    /**
     * Get the selection bounds in screen coordinates
     */
    fun getSelectionBounds(scale: Float): RectF? {
        if (selectionStartIndex < 0 || selectionEndIndex < 0) return null

        val startIdx = minOf(selectionStartIndex, selectionEndIndex)
        val endIdx = maxOf(selectionStartIndex, selectionEndIndex)

        if (startIdx >= textPositions.size || endIdx >= textPositions.size) return null

        val firstPos = textPositions[startIdx]
        val lastPos = textPositions[endIdx]

        return RectF(
            firstPos.x * scale,
            (firstPos.y - firstPos.height) * scale,
            (lastPos.x + lastPos.width) * scale,
            lastPos.y * scale
        )
    }

    /**
     * Get the start handle position
     */
    fun getStartHandlePosition(scale: Float): PointF? {
        if (selectionStartIndex < 0 || selectionStartIndex >= textPositions.size) return null

        val realStartIdx = minOf(selectionStartIndex, selectionEndIndex)
        val pos = textPositions[realStartIdx]

        return PointF(
            pos.x * scale,
            pos.y * scale
        )
    }

    /**
     * Get the end handle position
     */
    fun getEndHandlePosition(scale: Float): PointF? {
        if (selectionEndIndex < 0 || selectionEndIndex >= textPositions.size) return null

        val realEndIdx = maxOf(selectionStartIndex, selectionEndIndex)
        val pos = textPositions[realEndIdx]

        return PointF(
            (pos.x + pos.width) * scale,
            pos.y * scale
        )
    }

    /**
     * Get the actual start index (the smaller of the two indices)
     */
    fun getStartIndex(): Int {
        return minOf(selectionStartIndex, selectionEndIndex)
    }

    /**
     * Get the actual end index (the larger of the two indices)
     */
    fun getEndIndex(): Int {
        return maxOf(selectionStartIndex, selectionEndIndex)
    }

    /**
     * Set the selection indices directly
     */
    fun setSelection(startIndex: Int, endIndex: Int) {
        if (startIndex < 0 || startIndex >= textPositions.size ||
            endIndex < 0 || endIndex >= textPositions.size) return

        selectionStartIndex = startIndex
        selectionEndIndex = endIndex
        updateSelectedText()
    }

    /**
     * Data class to hold text position information
     */
    data class TextPositionInfo(
        val char: String,
        val x: Float,
        val y: Float,
        val width: Float,
        val height: Float,
        var endOfLine: Boolean,
        val fontSizeInPt: Float
    )

    companion object {
        private const val TAG = "PdfTextSelector"
    }
}
