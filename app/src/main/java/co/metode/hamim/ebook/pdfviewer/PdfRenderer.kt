package co.metode.hamim.ebook.pdfviewer

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.tom_roush.pdfbox.android.PDFBoxResourceLoader
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.rendering.PDFRenderer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import okhttp3.OkHttpClient
import okhttp3.Request

/**
 * Class for handling PDF rendering operations
 */
class PdfRenderer(private val context: Context) {

    private var pdfDocument: PDDocument? = null
    private var renderer: PDFRenderer? = null
    private var totalPages: Int = 0
    private var currentScale: Float = 2.0f // Default rendering scale

    init {
        // Initialize PDFBox
        PDFBoxResourceLoader.init(context)
    }

    /**
     * Open PDF document from file
     */
    suspend fun openDocument(file: File): Result<Int> {
        return try {
            // Close any previously opened document
            closeDocument()

            // Open the PDF with PDFBox
            pdfDocument = PDDocument.load(file)
            renderer = PDFRenderer(pdfDocument)

            totalPages = pdfDocument!!.numberOfPages
            Log.d(TAG, "PDFBox loaded document with $totalPages pages")

            Result.success(totalPages)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening PDF document", e)
            Result.failure(e)
        }
    }

    /**
     * Download PDF from URL and open it
     */
    suspend fun downloadAndOpenDocument(url: String): Result<Int> {
        return try {
            // Create a temporary file to store the downloaded PDF
            val tempFile = File(context.cacheDir, "temp_${System.currentTimeMillis()}.pdf")

            // Download the file
            val client = OkHttpClient()
            val request = Request.Builder().url(url).build()
            val response = client.newCall(request).execute()

            if (!response.isSuccessful) {
                throw IOException("Failed to download PDF: ${response.code}")
            }

            // Save the response body to a file
            response.body?.let { body ->
                tempFile.outputStream().use { output ->
                    body.byteStream().use { input ->
                        input.copyTo(output)
                    }
                }

                // Open the downloaded PDF
                return openDocument(tempFile)
            } ?: throw IOException("Empty response body")
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading PDF", e)
            Result.failure(e)
        }
    }

    /**
     * Render a specific page
     */
    suspend fun renderPage(pageIndex: Int): Result<Bitmap> {
        return withContext(Dispatchers.IO) {
            try {
                val document = pdfDocument
                val pdfRenderer = renderer

                if (document == null || pdfRenderer == null) {
                    return@withContext Result.failure(IllegalStateException("PDF document not loaded"))
                }

                if (pageIndex < 0 || pageIndex >= document.numberOfPages) {
                    return@withContext Result.failure(IndexOutOfBoundsException("Invalid page index: $pageIndex"))
                }

                // Render the page
                val bitmap = pdfRenderer.renderImage(pageIndex, currentScale)
                Result.success(bitmap)
            } catch (e: Exception) {
                Log.e(TAG, "Error rendering page", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get the total number of pages
     */
    fun getTotalPages(): Int {
        return totalPages
    }

    /**
     * Set the rendering scale
     */
    fun setScale(scale: Float) {
        currentScale = scale
    }

    /**
     * Get the current rendering scale
     */
    fun getScale(): Float {
        return currentScale
    }

    /**
     * Get the PDDocument instance
     */
    fun getDocument(): PDDocument? {
        return pdfDocument
    }

    /**
     * Close the PDF document
     */
    fun closeDocument() {
        try {
            pdfDocument?.close()
            pdfDocument = null
            renderer = null
        } catch (e: IOException) {
            Log.e(TAG, "Error closing PDF document", e)
        }
    }

    companion object {
        private const val TAG = "PdfRenderer"
    }
}
