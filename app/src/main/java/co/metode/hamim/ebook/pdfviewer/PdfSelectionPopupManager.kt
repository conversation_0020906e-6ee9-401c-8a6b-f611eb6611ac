package co.metode.hamim.ebook.pdfviewer

import android.content.Context
import android.graphics.Matrix
import android.graphics.RectF
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.PopupWindow
import co.metode.hamim.R

/**
 * Class for managing text selection popup menu
 */
class PdfSelectionPopupManager(
    private val context: Context,
    private val container: FrameLayout,
    private val clipboardManager: PdfClipboardManager
) {
    // Popup window and anchor view
    private var selectionMenuAnchorView: View? = null
    private var selectionPopupWindow: PopupWindow? = null

    // Callback interface
    interface PopupCallback {
        fun onPopupDismissed()
    }

    private var callback: PopupCallback? = null

    /**
     * Set the callback for popup events
     */
    fun setCallback(callback: PopupCallback) {
        this.callback = callback
    }

    /**
     * Show text selection menu at the specified position
     */
    fun showSelectionMenu(bounds: RectF, selectedText: String, matrix: Matrix) {
        if (selectedText.isBlank()) {
            dismissMenu()
            return
        }

        // Log untuk debugging
        android.util.Log.d("PdfSelectionPopupManager", "showSelectionMenu called with text: $selectedText")

        // Remove any existing menu
        dismissMenu()

        // Create a view to anchor the popup menu
        selectionMenuAnchorView = View(context)
        container.addView(selectionMenuAnchorView)

        // Position the anchor above the selection to avoid covering it
        val centerX = (bounds.left + bounds.right) / 2
        val topY = bounds.top - 100f  // Place menu closer to the selection (was 200f)

        // Get the position in the container coordinates
        val values = FloatArray(9)
        matrix.getValues(values)

        // Calculate screen coordinates - use default values if matrix is empty
        val translateX = if (values[Matrix.MTRANS_X] != 0f) values[Matrix.MTRANS_X] else 0f
        val translateY = if (values[Matrix.MTRANS_Y] != 0f) values[Matrix.MTRANS_Y] else 0f
        val scaleX = if (values[Matrix.MSCALE_X] != 0f) values[Matrix.MSCALE_X] else 1f
        val scaleY = if (values[Matrix.MSCALE_Y] != 0f) values[Matrix.MSCALE_Y] else 1f

        // Set anchor position
        selectionMenuAnchorView?.x = translateX + centerX * scaleX
        val calculatedY = translateY + topY * scaleY
        selectionMenuAnchorView?.y = if (calculatedY < 100) 100f else calculatedY
        selectionMenuAnchorView?.layoutParams = FrameLayout.LayoutParams(1, 1).apply {
            width = 1
            height = 1
        }

        // Log posisi anchor untuk debugging
        android.util.Log.d("PdfSelectionPopupManager", "Anchor position: x=${selectionMenuAnchorView?.x}, y=${selectionMenuAnchorView?.y}")

        // Inflate the popup layout
        val inflater = LayoutInflater.from(context)
        val popupView = inflater.inflate(R.layout.popup_text_selection, null)

        // Create the popup window
        selectionPopupWindow = PopupWindow(
            popupView,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true // Focusable
        )

        // Set elevation to create shadow
        selectionPopupWindow?.elevation = 10f

        // Set up button click listeners
        val btnCopy = popupView.findViewById<Button>(R.id.btn_copy)

        btnCopy.setOnClickListener {
            clipboardManager.copyToClipboard(selectedText)
            dismissMenu()
        }

        // Log sebelum menampilkan popup
        android.util.Log.d("PdfSelectionPopupManager", "About to show popup at x=${selectionMenuAnchorView?.x?.toInt()}, y=${selectionMenuAnchorView?.y?.toInt()}")

        try {
            // Show the popup window at the specified location
            selectionPopupWindow?.showAtLocation(
                container,
                Gravity.NO_GRAVITY,
                selectionMenuAnchorView?.x?.toInt() ?: 0,
                selectionMenuAnchorView?.y?.toInt() ?: 0
            )

            // Log setelah menampilkan popup
            android.util.Log.d("PdfSelectionPopupManager", "Popup shown successfully")
        } catch (e: Exception) {
            // Log error jika terjadi masalah
            android.util.Log.e("PdfSelectionPopupManager", "Error showing popup", e)
        }
    }

    /**
     * Update the position of the selection menu
     */
    fun updateMenuPosition(bounds: RectF, matrix: Matrix) {
        val popupWindow = selectionPopupWindow ?: return
        if (!popupWindow.isShowing) return

        // Log untuk debugging
        android.util.Log.d("PdfSelectionPopupManager", "updateMenuPosition called")

        // Get the position in the container coordinates
        val values = FloatArray(9)
        matrix.getValues(values)

        // Calculate screen coordinates - use default values if matrix is empty
        val translateX = if (values[Matrix.MTRANS_X] != 0f) values[Matrix.MTRANS_X] else 0f
        val translateY = if (values[Matrix.MTRANS_Y] != 0f) values[Matrix.MTRANS_Y] else 0f
        val scaleX = if (values[Matrix.MSCALE_X] != 0f) values[Matrix.MSCALE_X] else 1f
        val scaleY = if (values[Matrix.MSCALE_Y] != 0f) values[Matrix.MSCALE_Y] else 1f

        // Calculate center and top of selection
        val centerX = (bounds.left + bounds.right) / 2
        val topY = bounds.top - 100f  // Keep menu closer to selection (was 200f)

        // Update anchor position
        selectionMenuAnchorView?.let { anchor ->
            anchor.x = translateX + centerX * scaleX
            val calculatedY = translateY + topY * scaleY
            anchor.y = if (calculatedY < 100) 100f else calculatedY

            // Log posisi anchor untuk debugging
            android.util.Log.d("PdfSelectionPopupManager", "Updated anchor position: x=${anchor.x}, y=${anchor.y}")

            // Update popup window position
            popupWindow.update(
                anchor.x.toInt(),
                anchor.y.toInt(),
                -1, // Keep current width
                -1, // Keep current height
                true
            )
        }
    }

    /**
     * Dismiss the text selection menu
     */
    fun dismissMenu() {
        selectionPopupWindow?.dismiss()
        selectionPopupWindow = null

        selectionMenuAnchorView?.let {
            container.removeView(it)
        }
        selectionMenuAnchorView = null

        // Notify callback
        callback?.onPopupDismissed()
    }

    /**
     * Check if the menu is showing
     */
    fun isMenuShowing(): Boolean {
        return selectionPopupWindow?.isShowing == true
    }
}
