package co.metode.hamim.ebook.database

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log
import co.metode.hamim.ebook.Book

/**
 * Database untuk menyimpan informasi buku yang diunduh
 */
class BookDatabase(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    companion object {
        private const val TAG = "BookDatabase"
        private const val DATABASE_NAME = "ebook_database"
        private const val DATABASE_VERSION = 1

        // Nama tabel dan kolom
        private const val TABLE_BOOKS = "books"
        private const val COLUMN_ID = "id"
        private const val COLUMN_TITLE = "title"
        private const val COLUMN_AUTHOR = "author"
        private const val COLUMN_YEAR = "year"
        private const val COLUMN_COVER_URL = "cover_url"
        private const val COLUMN_IS_PURCHASED = "is_purchased"
        private const val COLUMN_IS_FREE = "is_free"
        private const val COLUMN_FILE_PATH = "file_path"
    }

    override fun onCreate(db: SQLiteDatabase) {
        val createTableQuery = """
            CREATE TABLE $TABLE_BOOKS (
                $COLUMN_ID TEXT PRIMARY KEY,
                $COLUMN_TITLE TEXT NOT NULL,
                $COLUMN_AUTHOR TEXT,
                $COLUMN_YEAR INTEGER,
                $COLUMN_COVER_URL TEXT,
                $COLUMN_IS_PURCHASED INTEGER,
                $COLUMN_IS_FREE INTEGER,
                $COLUMN_FILE_PATH TEXT
            )
        """.trimIndent()

        db.execSQL(createTableQuery)
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        db.execSQL("DROP TABLE IF EXISTS $TABLE_BOOKS")
        onCreate(db)
    }

    /**
     * Menyimpan buku ke database
     */
    fun saveBook(book: Book, filePath: String): Long {
        val db = this.writableDatabase
        val values = ContentValues().apply {
            put(COLUMN_ID, book.id)
            put(COLUMN_TITLE, book.title)
            put(COLUMN_AUTHOR, book.author)
            put(COLUMN_YEAR, book.year)
            put(COLUMN_COVER_URL, book.coverUrl)
            put(COLUMN_IS_PURCHASED, if (book.isPurchased) 1 else 0)
            put(COLUMN_IS_FREE, if (book.isFree) 1 else 0)
            put(COLUMN_FILE_PATH, filePath)
        }

        val result = db.insertWithOnConflict(TABLE_BOOKS, null, values, SQLiteDatabase.CONFLICT_REPLACE)
        db.close()

        if (result != -1L) {
            Log.d(TAG, "Book saved to database: ${book.title}")
        } else {
            Log.e(TAG, "Failed to save book to database: ${book.title}")
        }

        return result
    }

    /**
     * Menghapus buku dari database
     */
    fun deleteBook(bookId: String): Int {
        val db = this.writableDatabase
        val result = db.delete(TABLE_BOOKS, "$COLUMN_ID = ?", arrayOf(bookId))
        db.close()

        if (result > 0) {
            Log.d(TAG, "Book deleted from database: $bookId")
        } else {
            Log.e(TAG, "Failed to delete book from database: $bookId")
        }

        return result
    }

    /**
     * Mendapatkan semua buku dari database
     */
    fun getAllBooks(): List<Book> {
        val bookList = mutableListOf<Book>()
        val selectQuery = "SELECT * FROM $TABLE_BOOKS"
        val db = this.readableDatabase
        val cursor = db.rawQuery(selectQuery, null)

        if (cursor.moveToFirst()) {
            do {
                val id = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_ID))
                val title = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_TITLE))
                val author = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_AUTHOR))
                val year = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_YEAR))
                val coverUrl = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_COVER_URL))
                val isPurchased = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_IS_PURCHASED)) == 1
                val isFree = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_IS_FREE)) == 1

                val book = Book(
                    id = id,
                    title = title,
                    author = author,
                    year = year,
                    coverUrl = coverUrl,
                    isPurchased = isPurchased,
                    isFree = isFree
                )
                bookList.add(book)
            } while (cursor.moveToNext())
        }

        cursor.close()
        db.close()

        return bookList
    }

    /**
     * Memeriksa apakah buku ada di database
     */
    fun isBookInDatabase(bookId: String): Boolean {
        val db = this.readableDatabase
        val cursor = db.query(
            TABLE_BOOKS,
            arrayOf(COLUMN_ID),
            "$COLUMN_ID = ?",
            arrayOf(bookId),
            null,
            null,
            null
        )

        val exists = cursor.count > 0
        cursor.close()
        db.close()

        return exists
    }

    /**
     * Mendapatkan file path buku
     */
    fun getBookFilePath(bookId: String): String? {
        val db = this.readableDatabase
        val cursor = db.query(
            TABLE_BOOKS,
            arrayOf(COLUMN_FILE_PATH),
            "$COLUMN_ID = ?",
            arrayOf(bookId),
            null,
            null,
            null
        )

        var filePath: String? = null
        if (cursor.moveToFirst()) {
            filePath = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FILE_PATH))
        }

        cursor.close()
        db.close()

        return filePath
    }
}
