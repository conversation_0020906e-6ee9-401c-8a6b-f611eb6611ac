package co.metode.hamim.ebook.pdfviewer.config

import android.content.Context
import com.github.barteksc.pdfviewer.PDFView
import com.github.barteksc.pdfviewer.util.FitPolicy

/**
 * Manages PDF viewer configuration and settings
 * Provides a centralized way to configure PDFView with consistent settings
 */
class PdfConfigurationManager(private val context: Context) {

    /**
     * Configure PDFView with default settings optimized for mobile viewing
     */
    fun configurePdfViewer(pdfView: PDFView): PDFView.Configurator {
        return pdfView
            .enableSwipe(true) // Enable swipe gestures
            .swipeHorizontal(false) // Vertical scrolling by default
            .enableDoubletap(true) // Enable double tap to zoom
            .defaultPage(0) // Start from first page
            .enableAnnotationRendering(true) // Render annotations
            .password(null) // No password by default
            .enableAntialiasing(true) // Better rendering quality
            .spacing(0) // No spacing between pages initially
            .autoSpacing(false) // Don't auto-adjust spacing
            .pageFitPolicy(FitPolicy.WIDTH) // Fit to width by default
            .fitEachPage(false) // Scale relative to largest page
            .pageSnap(false) // Don't snap to page boundaries
            .pageFling(false) // Don't use fling for page changes
            .nightMode(false) // Day mode by default
    }

    /**
     * Configure PDFView for horizontal reading (like magazines)
     */
    fun configureHorizontalReading(pdfView: PDFView): PDFView.Configurator {
        return configurePdfViewer(pdfView)
            .swipeHorizontal(true)
            .pageSnap(true)
            .autoSpacing(true)
            .pageFling(true)
            .pageFitPolicy(FitPolicy.BOTH)
    }

    /**
     * Configure PDFView for book-like reading experience
     */
    fun configureBookReading(pdfView: PDFView): PDFView.Configurator {
        return configurePdfViewer(pdfView)
            .swipeHorizontal(false)
            .pageSnap(false)
            .autoSpacing(false)
            .spacing(8) // Small spacing between pages
            .pageFitPolicy(FitPolicy.WIDTH)
            .fitEachPage(true)
    }

    /**
     * Configure PDFView for presentation mode
     */
    fun configurePresentationMode(pdfView: PDFView): PDFView.Configurator {
        return configurePdfViewer(pdfView)
            .swipeHorizontal(true)
            .pageSnap(true)
            .autoSpacing(true)
            .pageFling(true)
            .pageFitPolicy(FitPolicy.BOTH)
            .fitEachPage(true)
    }

    /**
     * Configure PDFView with custom settings
     */
    fun configureCustom(
        pdfView: PDFView,
        isHorizontal: Boolean = false,
        enablePageSnap: Boolean = false,
        enableAutoSpacing: Boolean = false,
        enablePageFling: Boolean = false,
        fitPolicy: FitPolicy = FitPolicy.WIDTH,
        spacing: Int = 0,
        fitEachPage: Boolean = false,
        enableNightMode: Boolean = false
    ): PDFView.Configurator {
        return pdfView
            .enableSwipe(true)
            .swipeHorizontal(isHorizontal)
            .enableDoubletap(true)
            .defaultPage(0)
            .enableAnnotationRendering(true)
            .password(null)
            .enableAntialiasing(true)
            .spacing(spacing)
            .autoSpacing(enableAutoSpacing)
            .pageFitPolicy(fitPolicy)
            .fitEachPage(fitEachPage)
            .pageSnap(enablePageSnap)
            .pageFling(enablePageFling)
            .nightMode(enableNightMode)
    }

    /**
     * Get zoom levels for different screen sizes
     */
    fun getZoomLevels(): Triple<Float, Float, Float> {
        val density = context.resources.displayMetrics.density
        return when {
            density <= 1.0f -> Triple(1.0f, 1.5f, 3.0f) // ldpi
            density <= 1.5f -> Triple(1.0f, 1.75f, 3.5f) // mdpi
            density <= 2.0f -> Triple(1.0f, 2.0f, 4.0f) // hdpi
            density <= 3.0f -> Triple(1.0f, 2.25f, 4.5f) // xhdpi
            else -> Triple(1.0f, 2.5f, 5.0f) // xxhdpi and above
        }
    }

    /**
     * Configure zoom levels for PDFView
     */
    fun configureZoomLevels(pdfView: PDFView) {
        val (minZoom, midZoom, maxZoom) = getZoomLevels()
        pdfView.setMinZoom(minZoom)
        pdfView.setMidZoom(midZoom)
        pdfView.setMaxZoom(maxZoom)
    }

    /**
     * Get recommended page preload count based on device performance
     */
    fun getPreloadCount(): Int {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory() / (1024 * 1024) // Convert to MB
        
        return when {
            maxMemory < 128 -> 1 // Low memory devices
            maxMemory < 256 -> 2 // Medium memory devices
            maxMemory < 512 -> 3 // High memory devices
            else -> 4 // Very high memory devices
        }
    }

    /**
     * Check if device supports high quality rendering
     */
    fun shouldUseHighQuality(): Boolean {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory() / (1024 * 1024) // Convert to MB
        val availableProcessors = runtime.availableProcessors()
        
        // Use high quality if device has enough memory and processing power
        return maxMemory >= 256 && availableProcessors >= 4
    }

    /**
     * Get optimal spacing based on screen size
     */
    fun getOptimalSpacing(): Int {
        val density = context.resources.displayMetrics.density
        return (8 * density).toInt() // 8dp converted to pixels
    }
}
