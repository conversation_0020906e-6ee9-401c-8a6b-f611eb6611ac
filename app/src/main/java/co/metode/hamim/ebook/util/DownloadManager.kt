package co.metode.hamim.ebook.util

import android.content.Context
import android.os.Environment
import android.util.Log
import co.metode.hamim.ebook.Book
import co.metode.hamim.ebook.database.BookDatabase
import java.io.File

/**
 * Kelas untuk mengelola file ebook yang diunduh
 */
class DownloadManager(private val context: Context) {

    companion object {
        private const val TAG = "DownloadManager"
        private const val EBOOKS_DIRECTORY = "ebooks"
    }

    // Database untuk menyimpan informasi buku
    private val bookDatabase = BookDatabase(context)

    /**
     * Mendapatkan direktori untuk menyimpan ebook
     */
    private fun getEbooksDirectory(): File {
        val directory = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), EBOOKS_DIRECTORY)
        if (!directory.exists()) {
            directory.mkdirs()
        }
        return directory
    }

    /**
     * Mendapatkan file ebook berdasarkan ID buku
     */
    fun getEbookFile(bookId: String, title: String): File {
        val fileName = "${title.replace(" ", "_")}_${bookId}.pdf"
        return File(getEbooksDirectory(), fileName)
    }

    /**
     * Memeriksa apakah buku sudah diunduh
     */
    fun isBookDownloaded(bookId: String, title: String): Boolean {
        val file = getEbookFile(bookId, title)
        return file.exists() && file.length() > 0
    }

    /**
     * Mendapatkan daftar semua buku yang sudah diunduh
     */
    fun getDownloadedBooks(): List<File> {
        val directory = getEbooksDirectory()
        return directory.listFiles()?.filter { it.isFile && it.extension.equals("pdf", ignoreCase = true) } ?: emptyList()
    }

    /**
     * Menghapus buku yang sudah diunduh
     */
    fun deleteDownloadedBook(bookId: String, title: String): Boolean {
        val file = getEbookFile(bookId, title)
        return if (file.exists()) {
            val deleted = file.delete()
            if (deleted) {
                Log.d(TAG, "Book deleted: $title ($bookId)")
                // Hapus juga dari database
                bookDatabase.deleteBook(bookId)
            } else {
                Log.e(TAG, "Failed to delete book: $title ($bookId)")
            }
            deleted
        } else {
            Log.d(TAG, "Book not found: $title ($bookId)")
            false
        }
    }

    /**
     * Menyimpan informasi buku ke database
     */
    fun saveBookInfo(book: Book) {
        val filePath = getEbookFile(book.id, book.title).absolutePath
        bookDatabase.saveBook(book, filePath)
    }

    /**
     * Mendapatkan semua buku yang tersimpan di database
     */
    fun getSavedBooks(): List<Book> {
        return bookDatabase.getAllBooks()
    }

    /**
     * Memeriksa apakah buku ada di database
     */
    fun isBookInDatabase(bookId: String): Boolean {
        return bookDatabase.isBookInDatabase(bookId)
    }

    /**
     * Mendapatkan file path buku dari database
     */
    fun getBookFilePath(bookId: String): String? {
        return bookDatabase.getBookFilePath(bookId)
    }
}
