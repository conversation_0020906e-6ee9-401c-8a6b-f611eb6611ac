package co.metode.hamim.ebook

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.animation.AnimationUtils
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import co.metode.hamim.R
import co.metode.hamim.api.ApiClient
import co.metode.hamim.api.ApiInterface
import co.metode.hamim.databinding.ActivityBookBinding
import co.metode.hamim.ebook.fragment.AllBooksFragment
import co.metode.hamim.ebook.fragment.BookPagerAdapter
import co.metode.hamim.ebook.fragment.FreeBooksFragment
import co.metode.hamim.ebook.fragment.PurchasedBooksFragment
import co.metode.hamim.ebook.model.EbookItem
import co.metode.hamim.ebook.util.DownloadManager
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayoutMediator
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import co.metode.hamim.api.ApiConfig

/**
 * Activity utama untuk menampilkan daftar buku dengan fitur pencarian, filter, dan refresh.
 */
class BookActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBookBinding
    private var originalBookList: List<Book> = emptyList()

    // Download manager untuk mengelola file ebook
    private lateinit var downloadManager: DownloadManager

    // Status koneksi
    private var isOfflineMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBookBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setStatusBarTransparent()

        // Initialize API client for proper URL handling
        ApiClient.init(applicationContext)

        // Log device and API information
        Log.d("BookActivity", "Device: ${ApiConfig.getDeviceInfo()}")
        Log.d("BookActivity", "Using Ebook API URL: ${ApiClient.getDevUrl()}")
        Log.d("BookActivity", "Running on: " + (if (Build.FINGERPRINT.startsWith("generic")) "Emulator" else "Physical Device"))

        // Inisialisasi download manager
        downloadManager = DownloadManager(this)

        // Periksa koneksi internet
        isOfflineMode = !isNetworkAvailable()

        // Tampilkan indikator offline jika dalam mode offline
        updateOfflineIndicator()

        // Menangani klik tombol kembali.
        binding.ivBack.setOnClickListener { finish() }

        // Menangani klik tombol pencarian untuk menampilkan SearchView.
        binding.btnSearch.setOnClickListener {
            showSearchView()
        }

        // Mengatur SearchView untuk pencarian buku.
        setupSearchView()
        // Mengatur SwipeRefreshLayout untuk me-refresh data buku.
        setupSwipeRefresh()
        // Mengatur ViewPager untuk menampilkan fragment buku dalam tab.
        setupViewPager()
        // Mengatur listener scroll RecyclerView untuk menampilkan/menyembunyikan SearchView.
        setupRecyclerViewScrollListener()

        // Menampilkan animasi loading saat data sedang dimuat.
        binding.lottieAnimationView.visibility = View.VISIBLE
        binding.loadingTextView.visibility = View.VISIBLE
        binding.lottieAnimationView.playAnimation()

        // Load data from API
        loadData()
    }

    /**
     * Mengatur listener scroll RecyclerView untuk menampilkan/menyembunyikan SearchView.
     */
    private fun setupRecyclerViewScrollListener() {
        var currentScrollListener: RecyclerView.OnScrollListener? = null

        fun updateScrollListener(recyclerView: RecyclerView?) {
            // Menghapus listener scroll sebelumnya jika ada.
            currentScrollListener?.let { recyclerView?.removeOnScrollListener(it) }
            currentScrollListener = null

            if (recyclerView == null) return

            // Membuat listener scroll baru.
            val newScrollListener = object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // Menyembunyikan SearchView saat scroll ke bawah.
                    if (dy > 0) {
                        if (binding.searchView.visibility != View.GONE) {
                            hideSearchView()
                        }
                    } else if (dy < 0 && !recyclerView.canScrollVertically(-1)) {
                        // Menyembunyikan SearchView saat scroll ke atas dan di posisi paling atas.
                        hideSearchView()
                    }
                }
            }

            // Menambahkan listener scroll baru ke RecyclerView.
            recyclerView.addOnScrollListener(newScrollListener)
            currentScrollListener = newScrollListener

            // Memastikan RecyclerView scroll ke posisi awal dan menyembunyikan SearchView saat layout selesai.
            recyclerView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    recyclerView.scrollToPosition(0)
                    if (!recyclerView.canScrollVertically(-1)) {
                        hideSearchView()
                    }
                    recyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                }
            })
        }

        updateScrollListener(null)

        // Mengatur listener perubahan halaman ViewPager untuk memperbarui listener scroll dan filter buku.
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                val newFragment = supportFragmentManager.findFragmentByTag("f${position}")
                // Untuk AllBooksFragment, kita tidak perlu mendapatkan recyclerView karena menggunakan ViewPager2
                val recyclerView = when (newFragment) {
                    is PurchasedBooksFragment -> newFragment.recyclerView
                    else -> null
                }
                updateScrollListener(recyclerView) // Update dan langsung scroll
                filterBooks(binding.searchView.query.toString())
            }
        })
    }

    /**
     * Menyembunyikan SearchView dengan animasi slide up.
     */
    private fun hideSearchView() {
        if (binding.searchView.visibility == View.VISIBLE) {
            val animation = AnimationUtils.loadAnimation(this, R.anim.slide_up)
            animation.setAnimationListener(object : android.view.animation.Animation.AnimationListener {
                override fun onAnimationStart(animation: android.view.animation.Animation?) {}
                override fun onAnimationEnd(animation: android.view.animation.Animation?) {
                    binding.searchView.visibility = View.GONE
                    binding.searchView.setQuery("", false)
                    binding.searchView.clearFocus()
                    filterBooks("")
                    binding.btnSearch.visibility = View.VISIBLE
                }
                override fun onAnimationRepeat(animation: android.view.animation.Animation?) {}
            })
            binding.searchView.startAnimation(animation)
        }
    }

    /**
     * Menampilkan SearchView dengan animasi slide down.
     */
    private fun showSearchView() {
        if (binding.searchView.visibility != View.VISIBLE) {
            binding.searchView.visibility = View.VISIBLE
            val animation = AnimationUtils.loadAnimation(this, R.anim.slide_down)
            binding.searchView.startAnimation(animation)
            binding.btnSearch.visibility = View.GONE
            binding.searchView.requestFocus()
        }
    }

    /**
     * Mengatur SwipeRefreshLayout untuk me-refresh data buku.
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }
    }

    /**
     * Mengatur ViewPager untuk menampilkan fragment buku dalam tab.
     */
    private fun setupViewPager() {
        val pagerAdapter = BookPagerAdapter(this)
        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.offscreenPageLimit = 2

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.tab_all)
                1 -> getString(R.string.tab_my_collection)
                else -> ""
            }
        }.attach()
    }

    /**
     * Mengatur SearchView untuk pencarian buku.
     */
    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                binding.searchView.clearFocus()
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                filterBooks(newText)
                return true
            }
        })
    }

    /**
     * Memfilter buku berdasarkan query pencarian dan memperbarui fragment.
     */
    private fun filterBooks(query: String?) {
        val filteredList: List<Book> = if (query.isNullOrBlank()) {
            originalBookList
        } else {
            val lowerCaseQuery = query.lowercase()
            originalBookList.filter { book ->
                book.title.lowercase().contains(lowerCaseQuery) ||
                        book.author.lowercase().contains(lowerCaseQuery)
            }
        }
        updateAllFragments(filteredList)
    }

    /**
     * Memuat data buku dari API atau database lokal dan memperbarui tampilan.
     */
    private fun loadData() {
        binding.lottieAnimationView.visibility = View.VISIBLE
        binding.loadingTextView.visibility = View.VISIBLE
        binding.lottieAnimationView.playAnimation()

        // Periksa koneksi internet
        isOfflineMode = !isNetworkAvailable()

        // Perbarui indikator offline
        updateOfflineIndicator()

        if (isOfflineMode) {
            // Mode offline: tampilkan buku dari database lokal
            loadOfflineBooks()
        } else {
            // Mode online: ambil data dari API
            loadOnlineBooks()
        }
    }

    /**
     * Memuat buku dari database lokal saat offline
     */
    private fun loadOfflineBooks() {
        // Ambil buku yang sudah diunduh dari database
        val savedBooks = downloadManager.getSavedBooks().toMutableList()

        // Tambahkan buku sample jika belum ada
        val sampleBook = Book(
            id = "sample_book",
            title = "Sample Book",
            author = "Sample Author",
            year = 2024,
            coverUrl = "", // Kosong karena menggunakan file lokal
            isPurchased = true,
            isFree = true
        )

        // Cek apakah sample book sudah ada di assets
        val assetManager = assets
        val assetFiles = assetManager.list("")

        if (assetFiles?.contains(Constants.DEFAULT_SAMPLE_PDF) == true) {
            // Tambahkan sample book jika belum ada di daftar
            if (!savedBooks.any { it.id == "sample_book" }) {
                savedBooks.add(sampleBook)
            }
        }

        if (savedBooks.isNotEmpty()) {
            originalBookList = savedBooks
            updateAllFragments(originalBookList)

            // Tampilkan pesan offline mode
            Snackbar.make(binding.root, "Mode Offline: Menampilkan buku yang tersimpan", Snackbar.LENGTH_LONG).show()

            Log.d("BookActivity", "Loaded ${savedBooks.size} books from local database")
        } else {
            // Jika tidak ada buku yang tersimpan, tampilkan pesan
            Toast.makeText(this, "Tidak ada buku tersimpan untuk mode offline", Toast.LENGTH_SHORT).show()
            originalBookList = emptyList()
            updateAllFragments(originalBookList)
        }

        binding.lottieAnimationView.visibility = View.GONE
        binding.loadingTextView.visibility = View.GONE
        binding.swipeRefresh.isRefreshing = false
    }

    /**
     * Memuat buku dari API saat online
     */
    private fun loadOnlineBooks() {
        // Show a message about connecting to the development server
        Toast.makeText(
            this@BookActivity,
            "Connecting to development server at ${ApiClient.getDevUrl()}...",
            Toast.LENGTH_LONG
        ).show()

        // Use the ebook client which automatically selects the right URL
        val apiInterface = ApiClient.getDevClient().create(ApiInterface::class.java)
        val call = apiInterface.getEbooks()

        call.enqueue(object : Callback<List<EbookItem>> {
            override fun onResponse(call: Call<List<EbookItem>>, response: Response<List<EbookItem>>) {
                if (response.isSuccessful && response.body() != null) {
                    val ebookItems = response.body()!!
                    val books = ebookItems.map { it.toBook() }

                    // Tandai buku yang sudah diunduh
                    val markedBooks = books.map { book ->
                        book.apply {
                            // Periksa apakah buku sudah diunduh
                            if (downloadManager.isBookInDatabase(book.id)) {
                                // Tandai sebagai sudah diunduh
                                Log.d("BookActivity", "Book ${book.title} is downloaded")
                            }
                        }
                    }

                    originalBookList = markedBooks
                    updateAllFragments(originalBookList)

                    Log.d("BookActivity", "Loaded ${books.size} books from API")
                } else {
                    Log.e("BookActivity", "Error loading books: ${response.code()}")

                    // Show a more detailed error message
                    val errorMessage = "Server error: ${response.code()} - ${response.message()}\n" +
                                      "Make sure your development server is running at ${ApiClient.getDevUrl()}"
                    Toast.makeText(this@BookActivity, errorMessage, Toast.LENGTH_LONG).show()

                    // Show a dialog with troubleshooting steps
                    showTroubleshootingDialog()

                    // Coba load dari database lokal
                    loadOfflineBooks()
                }

                binding.lottieAnimationView.visibility = View.GONE
                binding.loadingTextView.visibility = View.GONE
                binding.swipeRefresh.isRefreshing = false
            }

            override fun onFailure(call: Call<List<EbookItem>>, t: Throwable) {
                Log.e("BookActivity", "API call failed", t)

                // Show a more detailed error message
                val errorMessage = when (t) {
                    is java.net.SocketTimeoutException -> {
                        "Connection timeout: Could not connect to server at ${ApiClient.getDevUrl()}\n" +
                        "Make sure your server is running and accessible."
                    }
                    is java.net.UnknownHostException -> {
                        "Unknown host: Could not resolve ${ApiClient.getDevUrl()}\n" +
                        "Check your network connection and server address."
                    }
                    else -> "Network error: ${t.message}"
                }

                Toast.makeText(this@BookActivity, errorMessage, Toast.LENGTH_LONG).show()

                // Show a dialog with troubleshooting steps
                showTroubleshootingDialog()

                // Load sample books for testing
                originalBookList = createDummyBooks()
                updateAllFragments(originalBookList)

                // Also try to load from database
                loadOfflineBooks()

                binding.lottieAnimationView.visibility = View.GONE
                binding.loadingTextView.visibility = View.GONE
                binding.swipeRefresh.isRefreshing = false
            }
        })
    }

    /**
     * Shows a dialog with troubleshooting steps for server connection issues
     */
    private fun showTroubleshootingDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("Connection Troubleshooting")
        builder.setMessage(
            "Could not connect to the development server. Please check:\n\n" +
            "1. Is your server running on port 8000?\n" +
            "2. Is your server listening on all interfaces (0.0.0.0)?\n" +
            "3. Is your firewall allowing connections to port 8000?\n" +
            "4. Are you on the same network as the server?\n" +
            "5. Is the IP address correct? Current: ${ApiClient.getDevUrl()}\n\n" +
            "For now, sample books will be shown for testing."
        )
        builder.setPositiveButton("OK") { dialog, _ -> dialog.dismiss() }
        builder.show()
    }

    /**
     * Creates dummy books for testing when the server is not available
     */
    private fun createDummyBooks(): List<Book> {
        return listOf(
            Book(id = "A", title = "Atomic Habits", author = "James Clear", year = 2018, coverUrl = "https://m.media-amazon.com/images/I/91bYsX41DVL._SL1500_.jpg", isPurchased = false, isFree = false),
            Book(id = "B", title = "As A Man Thinketh", author = "James Allen", year = 2016, coverUrl = "https://m.media-amazon.com/images/I/81tEgsxpNZS._SL1500_.jpg", isPurchased = false, isFree = true),
            Book(id = "C", title = "The Subtle Art of Not Giving a F*ck", author = "Mark Manson", year = 2016, coverUrl = "https://m.media-amazon.com/images/I/71QKQ9mwV7L._SL1500_.jpg", isPurchased = true, isFree = false),
            Book(id = "D", title = "Think and Grow Rich", author = "Napoleon Hill", year = 1937, coverUrl = "https://m.media-amazon.com/images/I/71UypkUjStL._SL1500_.jpg", isPurchased = false, isFree = true),
            Book(id = "E", title = "Rich Dad Poor Dad", author = "Robert T. Kiyosaki", year = 1997, coverUrl = "https://m.media-amazon.com/images/I/81bsw6fnUiL._SL1500_.jpg", isPurchased = true, isFree = false),
            Book(id = "F", title = "The Alchemist", author = "Paulo Coelho", year = 1988, coverUrl = "https://m.media-amazon.com/images/I/71aFt4+OTOL._SL1500_.jpg", isPurchased = false, isFree = true),
            Book(id = "G", title = "Ikigai: The Japanese Secret to a Long and Happy Life", author = "Héctor García, Francesc Miralles", year = 2016, coverUrl = "https://m.media-amazon.com/images/I/81l3rZK4lnL._SL1500_.jpg", isPurchased = false, isFree = true),
            Book(id = "H", title = "Sapiens: A Brief History of Humankind", author = "Yuval Noah Harari", year = 2011, coverUrl = "https://m.media-amazon.com/images/I/713jIoMO3UL.jpg", isPurchased = true, isFree = false),
            Book(id = "I", title = "Educated", author = "Tara Westover", year = 2018, coverUrl = "https://m.media-amazon.com/images/I/81WojUxbbFL.jpg", isPurchased = false, isFree = false),
            Book(id = "J", title = "Becoming", author = "Michelle Obama", year = 2018, coverUrl = "https://m.media-amazon.com/images/I/81h2gWPTYJL.jpg", isPurchased = true, isFree = false),
            Book(id = "K", title = "The Catcher in the Rye", author = "J.D. Salinger", year = 1951, coverUrl = "https://m.media-amazon.com/images/I/81OthjkJBuL.jpg", isPurchased = false, isFree = true),
            Book(id = "L", title = "The Hobbit", author = "J.R.R. Tolkien", year = 1937, coverUrl = "https://m.media-amazon.com/images/I/91b0C2YNSrL.jpg", isPurchased = true, isFree = false),
            Book(id = "M", title = "Fahrenheit 451", author = "Ray Bradbury", year = 1953, coverUrl = "https://m.media-amazon.com/images/I/81bsw6fnUiL.jpg", isPurchased = false, isFree = true),
            Book(id = "N", title = "To Kill a Mockingbird", author = "Harper Lee", year = 1960, coverUrl = "https://m.media-amazon.com/images/I/81aY1lxk+9L.jpg", isPurchased = false, isFree = false),
            Book(id = "O", title = "The Great Gatsby", author = "F. Scott Fitzgerald", year = 1925, coverUrl = "https://m.media-amazon.com/images/I/81af+MCATTL.jpg", isPurchased = true, isFree = false)
        )
    }

    /**
     * Memperbarui data buku di semua fragment.
     */
    private fun updateAllFragments(books: List<Book>) {
        val adapter = binding.viewPager.adapter as? BookPagerAdapter
        adapter?.let {
            for (i in 0 until it.itemCount) {
                val fragment = it.getFragment(i)
                val filteredBooks = when (i) {
                    0 -> books // Semua buku.
                    1 -> books.filter { it.isPurchased } // Koleksiku (buku yang dibeli).
                    else -> continue
                }
                when (fragment) {
                    is AllBooksFragment -> fragment.setBooks(filteredBooks)
                    is PurchasedBooksFragment -> fragment.setBooks(filteredBooks)
                }
            }
        }
    }

    /**
     * Membuka Activity detail buku.
     */
    internal fun openDetailActivity(book: Book) {
        val intent = Intent(this, BookDetailActivity::class.java)
        intent.putExtra("book", book)
        startActivity(intent)
    }

    /**
     * Mengatur status bar transparan.
     */
    private fun setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = Color.TRANSPARENT
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            }
        }
    }

    /**
     * Memeriksa apakah perangkat terhubung ke internet
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.isConnected
        }
    }

    /**
     * Memperbarui tampilan indikator offline
     */
    private fun updateOfflineIndicator() {
        val offlineIndicator = findViewById<TextView>(R.id.offlineIndicator)

        if (isOfflineMode) {
            offlineIndicator?.visibility = View.VISIBLE
        } else {
            offlineIndicator?.visibility = View.GONE
        }
    }
}