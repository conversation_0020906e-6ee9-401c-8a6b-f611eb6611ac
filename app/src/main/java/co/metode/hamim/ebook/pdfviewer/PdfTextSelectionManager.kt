package co.metode.hamim.ebook.pdfviewer

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import android.util.Log
import android.view.View
import android.widget.ImageView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Class for managing text selection in PDF documents
 */
class PdfTextSelectionManager(
    private val pdfTextSelector: PdfTextSelector,
    private val coroutineScope: CoroutineScope
) {
    // Selection state
    private var isSelectionMode = false
    private var isStartHandleDragging = false
    private var isEndHandleDragging = false
    private var startHandleIndex = -1
    private var endHandleIndex = -1

    // Original bitmap without selection
    private var lastRenderedBitmap: Bitmap? = null

    // Job for debouncing selection menu
    private var selectionMenuJob: Job? = null

    // Selection popup manager
    private var popupManager: PdfSelectionPopupManager? = null

    // Callback interface
    interface SelectionCallback {
        fun onSelectionChanged(bounds: RectF?, selectedText: String)
        fun onSelectionHandlesUpdated(startPos: PointF?, endPos: PointF?)
        fun onSelectionCleared()
        fun updateBitmap(bitmap: Bitmap)
    }

    private var callback: SelectionCallback? = null

    /**
     * Set the callback for selection events
     */
    fun setCallback(callback: SelectionCallback) {
        this.callback = callback
    }

    /**
     * Set the popup manager
     */
    fun setPopupManager(popupManager: PdfSelectionPopupManager) {
        this.popupManager = popupManager
    }

    /**
     * Set the last rendered bitmap (without selection)
     */
    fun setLastRenderedBitmap(bitmap: Bitmap) {
        lastRenderedBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
    }

    /**
     * Start text selection at the given coordinates
     */
    fun startSelection(x: Float, y: Float, scale: Float) {
        // Log untuk debugging
        android.util.Log.d("PdfTextSelectionManager", "startSelection called at x=$x, y=$y")

        // Find the nearest text position
        val index = pdfTextSelector.findNearestPosition(x, y, scale)
        android.util.Log.d("PdfTextSelectionManager", "Nearest text position index: $index")

        if (index >= 0) {
            // Start selection
            pdfTextSelector.startSelection(index)
            isSelectionMode = true

            // Update the display on main thread
            coroutineScope.launch(Dispatchers.Main) {
                updateSelectionDisplay(scale)

                // Log untuk debugging
                android.util.Log.d("PdfTextSelectionManager", "Selection mode: $isSelectionMode")
                android.util.Log.d("PdfTextSelectionManager", "Selected text: ${pdfTextSelector.getSelectedText()}")
            }
        } else {
            android.util.Log.e("PdfTextSelectionManager", "Could not find nearest position")
        }
    }

    /**
     * Temukan awal kata dari posisi indeks
     */
    fun findWordStart(index: Int): Int {
        var startIndex = index
        while (startIndex > 0) {
            val text = pdfTextSelector.getCharAt(startIndex - 1)
            if (text.isEmpty() || text.contains(" ") || text.contains("\n") || text.contains("\t")) {
                break
            }
            startIndex--
        }
        return startIndex
    }

    /**
     * Temukan akhir kata dari posisi indeks
     */
    fun findWordEnd(index: Int): Int {
        var endIndex = index
        val maxIndex = pdfTextSelector.getTextPositionsCount() - 1
        while (endIndex < maxIndex) {
            val text = pdfTextSelector.getCharAt(endIndex + 1)
            if (text.isEmpty() || text.contains(" ") || text.contains("\n") || text.contains("\t")) {
                break
            }
            endIndex++
        }
        return endIndex
    }

    /**
     * Update text selection as user drags
     */
    fun updateSelection(x: Float, y: Float, scale: Float) {
        // Log untuk debugging
        android.util.Log.d("PdfTextSelectionManager", "updateSelection called at x=$x, y=$y")

        // Find the nearest text position
        val index = pdfTextSelector.findNearestPosition(x, y, scale)
        if (index >= 0) {
            // Update selection
            pdfTextSelector.updateSelection(index)

            // Update the display on main thread
            coroutineScope.launch(Dispatchers.Main) {
                updateSelectionDisplay(scale)
            }
        }
    }

    /**
     * Update the display to show the current text selection
     */
    private fun updateSelectionDisplay(scale: Float) {
        val bitmap = lastRenderedBitmap ?: return

        // Draw selection on bitmap
        val selectionBitmap = pdfTextSelector.drawSelection(bitmap, scale)

        // Update UI on main thread
        coroutineScope.launch(Dispatchers.Main) {
            // Update image view through callback
            callback?.updateBitmap(selectionBitmap)

            // Update selection handles positions
            updateSelectionHandles(scale)

            // Get selection bounds
            val bounds = pdfTextSelector.getSelectionBounds(scale)

            // Get selected text
            val selectedText = pdfTextSelector.getSelectedText()

            // Notify callback
            callback?.onSelectionChanged(bounds, selectedText)

            // Cancel previous job if exists
            selectionMenuJob?.cancel()

            // Start new debounce job
            selectionMenuJob = launch {
                delay(100) // Wait 100ms before showing/updating menu (reduced from 300ms)

                // Show or update popup menu
                if (bounds != null && selectedText.isNotBlank()) {
                    // Log untuk debugging
                    Log.d("PdfTextSelectionManager", "Showing menu for text: $selectedText")

                    // Gunakan Matrix kosong jika callback bukan ImageView
                    val matrix = Matrix()

                    // Tampilkan atau perbarui menu popup
                    if (popupManager?.isMenuShowing() == true) {
                        Log.d("PdfTextSelectionManager", "Updating menu position")
                        popupManager?.updateMenuPosition(bounds, matrix)
                    } else {
                        Log.d("PdfTextSelectionManager", "Showing new menu")
                        popupManager?.showSelectionMenu(bounds, selectedText, matrix)
                    }
                } else {
                    Log.d("PdfTextSelectionManager", "Not showing menu: bounds=$bounds, text=${selectedText.isNotBlank()}")
                }
            }
        }
    }

    /**
     * Update the position of selection handles
     */
    private fun updateSelectionHandles(scale: Float) {
        // Get handle positions
        val startPos = pdfTextSelector.getStartHandlePosition(scale)
        val endPos = pdfTextSelector.getEndHandlePosition(scale)

        if (startPos != null && endPos != null) {
            // Store current indices for handle dragging
            startHandleIndex = pdfTextSelector.getStartIndex()
            endHandleIndex = pdfTextSelector.getEndIndex()

            // Notify callback (already on main thread from caller)
            callback?.onSelectionHandlesUpdated(startPos, endPos)
        }
    }

    /**
     * Set selection with specific start and end indices
     */
    fun setSelection(startIndex: Int, endIndex: Int, scale: Float) {
        pdfTextSelector.setSelection(startIndex, endIndex)

        // Make sure we're on the main thread when updating UI
        coroutineScope.launch(Dispatchers.Main) {
            updateSelectionDisplay(scale)
        }
    }

    /**
     * Clear text selection
     */
    fun clearSelection() {
        if (isSelectionMode) {
            // Clear selection
            pdfTextSelector.clearSelection()
            isSelectionMode = false
            isStartHandleDragging = false
            isEndHandleDragging = false

            // Make sure we're on the main thread when updating UI
            coroutineScope.launch(Dispatchers.Main) {
                // Cancel any pending menu job
                selectionMenuJob?.cancel()

                // Dismiss selection menu
                popupManager?.dismissMenu()

                // Notify callback
                callback?.onSelectionCleared()

                // Restore original bitmap
                lastRenderedBitmap?.let {
                    callback?.updateBitmap(it)
                }
            }
        }
    }

    /**
     * Get the selected text
     */
    fun getSelectedText(): String {
        return pdfTextSelector.getSelectedText()
    }

    /**
     * Check if selection mode is active
     */
    fun isInSelectionMode(): Boolean {
        return isSelectionMode
    }

    /**
     * Set start handle dragging state
     */
    fun setStartHandleDragging(dragging: Boolean) {
        isStartHandleDragging = dragging
    }

    /**
     * Set end handle dragging state
     */
    fun setEndHandleDragging(dragging: Boolean) {
        isEndHandleDragging = dragging
    }

    /**
     * Check if start handle is being dragged
     */
    fun isStartHandleDragging(): Boolean {
        return isStartHandleDragging
    }

    /**
     * Check if end handle is being dragged
     */
    fun isEndHandleDragging(): Boolean {
        return isEndHandleDragging
    }

    /**
     * Get start handle index
     */
    fun getStartHandleIndex(): Int {
        return startHandleIndex
    }

    /**
     * Get end handle index
     */
    fun getEndHandleIndex(): Int {
        return endHandleIndex
    }

    /**
     * Find the nearest text position to the given coordinates
     */
    fun findNearestPosition(x: Float, y: Float, scale: Float): Int {
        return pdfTextSelector.findNearestPosition(x, y, scale)
    }

    /**
     * Get position information at the given index
     */
    fun getPositionAt(index: Int): PdfTextSelector.TextPositionInfo? {
        return pdfTextSelector.getPositionAt(index)
    }

    /**
     * Check if there is text at the given position using PDFBox
     */
    fun hasTextAtPosition(x: Float, y: Float, scale: Float): Boolean {
        return pdfTextSelector.hasTextAtPosition(x, y, scale)
    }
}
