package co.metode.hamim.ebook.pdfviewer

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import co.metode.hamim.R
import co.metode.hamim.databinding.ActivityPdfViewerBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

/**
 * Activity for viewing PDF files
 */
class PdfViewerActivity : AppCompatActivity(),
    PdfTextSelectionManager.SelectionCallback,
    PdfGestureHandler.GestureCallback,
    PdfSelectionPopupManager.PopupCallback {

    private lateinit var binding: ActivityPdfViewerBinding

    // PDF components
    private lateinit var pdfRenderer: PdfRenderer
    private lateinit var pdfTextSelector: PdfTextSelector
    private lateinit var textSelectionManager: PdfTextSelectionManager
    private lateinit var clipboardManager: PdfClipboardManager
    private lateinit var selectionPopupManager: PdfSelectionPopupManager
    private lateinit var gestureHandler: PdfGestureHandler

    // PDF metadata
    private var currentPage = 0
    private var totalPages = 0
    private var bookId: String? = null
    private var bookTitle: String? = null
    private var pdfPath: String? = null
    private var pdfUrl: String? = null

    // Coroutine scope for background operations
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPdfViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        // Get data from intent
        bookId = intent.getStringExtra("book_id")
        bookTitle = intent.getStringExtra("book_title")
        pdfPath = intent.getStringExtra("pdf_path")
        pdfUrl = intent.getStringExtra("pdf_url")

        // Set title
        supportActionBar?.title = bookTitle ?: "PDF Viewer"

        // Initialize components
        initializeComponents()

        // Set up UI listeners
        setupListeners()

        // Load PDF
        loadPdf()
    }

    /**
     * Initialize PDF components
     */
    private fun initializeComponents() {
        // Initialize PDF renderer
        pdfRenderer = PdfRenderer(this)

        // Initialize clipboard manager
        clipboardManager = PdfClipboardManager(this)

        // Initialize selection popup manager
        selectionPopupManager = PdfSelectionPopupManager(
            this,
            binding.pdfContainer,
            clipboardManager
        )
        selectionPopupManager.setCallback(this)
    }

    /**
     * Set up UI listeners
     */
    private fun setupListeners() {
        // Previous page button
        binding.btnPrevPage.setOnClickListener {
            if (currentPage > 0) {
                currentPage--
                renderPage(currentPage)
            }
        }

        // Next page button
        binding.btnNextPage.setOnClickListener {
            if (currentPage < totalPages - 1) {
                currentPage++
                renderPage(currentPage)
            }
        }
    }

    /**
     * Load PDF from file or URL
     */
    private fun loadPdf() {
        binding.progressBar.visibility = View.VISIBLE

        coroutineScope.launch(Dispatchers.IO) {
            try {
                val result = when {
                    // Load from local file path
                    !pdfPath.isNullOrEmpty() -> {
                        val file = File(pdfPath!!)
                        if (file.exists()) {
                            pdfRenderer.openDocument(file)
                        } else {
                            Result.failure(IOException("PDF file not found"))
                        }
                    }
                    // Load from URL
                    !pdfUrl.isNullOrEmpty() -> {
                        pdfRenderer.downloadAndOpenDocument(pdfUrl!!)
                    }
                    // No source provided
                    else -> {
                        Result.failure(IllegalArgumentException("No PDF source provided"))
                    }
                }

                result.fold(
                    onSuccess = { pages ->
                        totalPages = pages

                        // Initialize text selector with the document
                        val document = pdfRenderer.getDocument()
                        if (document != null) {
                            pdfTextSelector = PdfTextSelector(document)

                            // Initialize text selection manager
                            textSelectionManager = PdfTextSelectionManager(
                                pdfTextSelector,
                                coroutineScope
                            )
                        }

                        withContext(Dispatchers.Main) {
                            if (document != null) {
                                // Set callbacks on main thread
                                textSelectionManager.setCallback(this@PdfViewerActivity)
                                textSelectionManager.setPopupManager(selectionPopupManager)

                                // Initialize gesture handler on main thread
                                gestureHandler = PdfGestureHandler(
                                    this@PdfViewerActivity,
                                    textSelectionManager,
                                    pdfRenderer.getScale()
                                )
                                gestureHandler.setCallback(this@PdfViewerActivity)

                                // Set up touch listeners
                                setupTouchListeners()
                            }

                            binding.progressBar.visibility = View.GONE
                            renderPage(0)
                        }
                    },
                    onFailure = { e ->
                        Log.e(TAG, "Error loading PDF", e)
                        showError(e.message ?: "Unknown error")
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error loading PDF", e)
                showError(e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Set up touch listeners for PDF view and selection handles
     */
    private fun setupTouchListeners() {
        // Set up touch listener for PDF image view
        binding.pdfImageView.setOnTouchListener { view, event ->
            gestureHandler.onTouchEvent(view, event)
        }

        // Set up touch listener for start selection handle
        binding.startSelectionHandle.setOnTouchListener { view, event ->
            gestureHandler.onStartHandleTouchEvent(
                view,
                event,
                binding.pdfImageView,
                binding.pdfImageView.imageMatrix
            )
        }

        // Set up touch listener for end selection handle
        binding.endSelectionHandle.setOnTouchListener { view, event ->
            gestureHandler.onEndHandleTouchEvent(
                view,
                event,
                binding.pdfImageView,
                binding.pdfImageView.imageMatrix
            )
        }
    }

    /**
     * Render a specific page
     */
    private fun renderPage(pageIndex: Int) {
        binding.progressBar.visibility = View.VISIBLE

        // Clear any existing selection (already on main thread)
        textSelectionManager.clearSelection()

        coroutineScope.launch(Dispatchers.IO) {
            val result = pdfRenderer.renderPage(pageIndex)

            result.fold(
                onSuccess = { bitmap ->
                    // Store the original bitmap in the selection manager
                    textSelectionManager.setLastRenderedBitmap(bitmap)

                    // Load text positions for this page
                    try {
                        pdfTextSelector.loadTextPositions(pageIndex)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error loading text positions", e)
                    }

                    withContext(Dispatchers.Main) {
                        // Update UI
                        binding.pdfImageView.setImageBitmap(bitmap)
                        binding.pageCounter.text = getString(
                            R.string.page_counter_format,
                            pageIndex + 1,
                            totalPages
                        )
                        binding.progressBar.visibility = View.GONE

                        // Update navigation buttons
                        binding.btnPrevPage.isEnabled = pageIndex > 0
                        binding.btnNextPage.isEnabled = pageIndex < totalPages - 1

                        // Update current page
                        currentPage = pageIndex
                    }
                },
                onFailure = { e ->
                    Log.e(TAG, "Error rendering page", e)
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        Toast.makeText(
                            this@PdfViewerActivity,
                            "Error rendering page: ${e.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            )
        }
    }

    /**
     * Show error message
     */
    private suspend fun showError(message: String) {
        withContext(Dispatchers.Main) {
            binding.progressBar.visibility = View.GONE
            binding.pdfImageView.visibility = View.GONE
            binding.pdfNotFoundLayout.visibility = View.VISIBLE
            binding.pdfNotFoundMessage.text = message
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_pdf_viewer, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_bookmark -> {
                // Handle bookmark action
                Toast.makeText(this, "Bookmark feature coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_search -> {
                // Handle search action
                Toast.makeText(this, "Search feature coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // Close PDF document
        pdfRenderer.closeDocument()
    }

    // PdfTextSelectionManager.SelectionCallback implementation

    override fun onSelectionChanged(bounds: RectF?, selectedText: String) {
        // This is handled by the selection manager
    }

    override fun onSelectionHandlesUpdated(startPos: PointF?, endPos: PointF?) {
        if (startPos != null && endPos != null) {
            // Calculate handle positions relative to the container
            val imageView = binding.pdfImageView

            // Get the image matrix to calculate the actual position on screen
            val matrix = imageView.imageMatrix
            val values = FloatArray(9)
            matrix.getValues(values)

            // Calculate the actual position on screen
            val translateX = values[Matrix.MTRANS_X]
            val translateY = values[Matrix.MTRANS_Y]
            val scaleX = values[Matrix.MSCALE_X]
            val scaleY = values[Matrix.MSCALE_Y]

            // Set start handle position
            val startX = translateX + startPos.x * scaleX
            val startY = translateY + startPos.y * scaleY
            binding.startSelectionHandle.x = startX - binding.startSelectionHandle.width / 2
            binding.startSelectionHandle.y = startY
            binding.startSelectionHandle.visibility = View.VISIBLE

            // Set end handle position
            val endX = translateX + endPos.x * scaleX
            val endY = translateY + endPos.y * scaleY
            binding.endSelectionHandle.x = endX - binding.endSelectionHandle.width / 2
            binding.endSelectionHandle.y = endY
            binding.endSelectionHandle.visibility = View.VISIBLE
        }
    }

    override fun onSelectionCleared() {
        // Hide selection handles
        binding.startSelectionHandle.visibility = View.GONE
        binding.endSelectionHandle.visibility = View.GONE
    }

    override fun updateBitmap(bitmap: Bitmap) {
        binding.pdfImageView.setImageBitmap(bitmap)
    }

    // PdfGestureHandler.GestureCallback implementation

    override fun onPageTap() {
        // Nothing to do
    }

    override fun onPageDoubleTap() {
        // Nothing to do
    }

    override fun onPageLongPress(x: Float, y: Float) {
        // Nothing to do, handled by the gesture handler
    }

    override fun onPageScroll(distanceX: Float, distanceY: Float) {
        // Nothing to do
    }

    override fun onPageFling(velocityX: Float, velocityY: Float) {
        // Nothing to do
    }

    // PdfSelectionPopupManager.PopupCallback implementation

    override fun onPopupDismissed() {
        // Nothing to do
    }

    companion object {
        private const val TAG = "PdfViewerActivity"
    }
}
