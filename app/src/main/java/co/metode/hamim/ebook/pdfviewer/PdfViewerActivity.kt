package co.metode.hamim.ebook.pdfviewer

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import co.metode.hamim.R
import co.metode.hamim.databinding.ActivityPdfViewerBinding
import co.metode.hamim.ebook.pdfviewer.config.PdfConfigurationManager
import co.metode.hamim.ebook.pdfviewer.handlers.PdfEventHandlers
import co.metode.hamim.ebook.pdfviewer.utils.PdfUtils
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle
import java.io.File

/**
 * Modern PDF Viewer Activity using AndroidPdfViewer library
 * Supports both local files and URLs with comprehensive configuration
 */
class PdfViewerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPdfViewerBinding
    private lateinit var configManager: PdfConfigurationManager
    private lateinit var eventHandlers: PdfEventHandlers
    private lateinit var pdfUtils: PdfUtils

    // PDF source information
    private var pdfPath: String? = null
    private var pdfUrl: String? = null
    private var bookTitle: String? = null
    private var bookId: String? = null

    // PDF state
    private var currentPage = 0
    private var totalPages = 0
    private var isNightMode = false

    companion object {
        private const val TAG = "PdfViewerActivity"
        
        // Intent extras
        const val EXTRA_PDF_PATH = "pdf_path"
        const val EXTRA_PDF_URL = "pdf_url"
        const val EXTRA_BOOK_TITLE = "book_title"
        const val EXTRA_BOOK_ID = "book_id"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPdfViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize components
        initializeComponents()
        
        // Setup toolbar
        setupToolbar()
        
        // Get intent data
        extractIntentData()
        
        // Load PDF
        loadPdf()
    }

    private fun initializeComponents() {
        configManager = PdfConfigurationManager(this)
        eventHandlers = PdfEventHandlers(this)
        pdfUtils = PdfUtils(this)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = bookTitle ?: getString(R.string.pdf_viewer_title)
        }
    }

    private fun extractIntentData() {
        pdfPath = intent.getStringExtra(EXTRA_PDF_PATH)
        pdfUrl = intent.getStringExtra(EXTRA_PDF_URL)
        bookTitle = intent.getStringExtra(EXTRA_BOOK_TITLE)
        bookId = intent.getStringExtra(EXTRA_BOOK_ID)

        Log.d(TAG, "PDF Path: $pdfPath")
        Log.d(TAG, "PDF URL: $pdfUrl")
        Log.d(TAG, "Book Title: $bookTitle")
    }

    private fun loadPdf() {
        when {
            !pdfPath.isNullOrEmpty() -> loadFromFile()
            !pdfUrl.isNullOrEmpty() -> loadFromUrl()
            else -> showError("No PDF source provided")
        }
    }

    private fun loadFromFile() {
        val file = File(pdfPath!!)
        if (!file.exists()) {
            showError("PDF file not found: ${file.absolutePath}")
            return
        }

        Log.d(TAG, "Loading PDF from file: ${file.absolutePath}")
        
        configManager.configurePdfViewer(binding.pdfView)
            .fromFile(file)
            .onLoad { pages ->
                totalPages = pages
                onPdfLoaded()
            }
            .onPageChange { page, _ ->
                currentPage = page
                updatePageInfo()
            }
            .onError { error ->
                Log.e(TAG, "Error loading PDF from file", error)
                showError("Error loading PDF: ${error.message}")
            }
            .scrollHandle(DefaultScrollHandle(this))
            .spacing(10)
            .load()
    }

    private fun loadFromUrl() {
        Log.d(TAG, "Loading PDF from URL: $pdfUrl")
        
        // Download PDF from URL first
        pdfUtils.downloadPdfFromUrl(pdfUrl!!) { downloadedFile ->
            if (downloadedFile != null) {
                // Load the downloaded file
                configManager.configurePdfViewer(binding.pdfView)
                    .fromFile(downloadedFile)
                    .onLoad { pages ->
                        totalPages = pages
                        onPdfLoaded()
                    }
                    .onPageChange { page, _ ->
                        currentPage = page
                        updatePageInfo()
                    }
                    .onError { error ->
                        Log.e(TAG, "Error loading downloaded PDF", error)
                        showError("Error loading PDF: ${error.message}")
                    }
                    .scrollHandle(DefaultScrollHandle(this))
                    .spacing(10)
                    .load()
            } else {
                showError("Failed to download PDF from URL")
            }
        }
    }

    private fun onPdfLoaded() {
        Log.d(TAG, "PDF loaded successfully with $totalPages pages")
        binding.progressBar.visibility = android.view.View.GONE
        binding.pdfView.visibility = android.view.View.VISIBLE
        updatePageInfo()
        
        Toast.makeText(this, "PDF loaded: $totalPages pages", Toast.LENGTH_SHORT).show()
    }

    private fun updatePageInfo() {
        val pageText = getString(R.string.page_info, currentPage + 1, totalPages)
        supportActionBar?.subtitle = pageText
    }

    private fun showError(message: String) {
        Log.e(TAG, message)
        binding.progressBar.visibility = android.view.View.GONE
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        
        // Optionally finish activity on error
        // finish()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_pdf_viewer, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_night_mode -> {
                toggleNightMode()
                true
            }
            R.id.action_zoom_in -> {
                zoomIn()
                true
            }
            R.id.action_zoom_out -> {
                zoomOut()
                true
            }
            R.id.action_fit_width -> {
                fitToWidth()
                true
            }
            R.id.action_share -> {
                sharePdf()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun toggleNightMode() {
        isNightMode = !isNightMode
        // Reload PDF with night mode
        binding.pdfView.nightMode(isNightMode)
        Toast.makeText(this, 
            if (isNightMode) "Night mode enabled" else "Night mode disabled", 
            Toast.LENGTH_SHORT).show()
    }

    private fun zoomIn() {
        val currentZoom = binding.pdfView.zoom
        binding.pdfView.zoomTo(currentZoom * 1.2f)
    }

    private fun zoomOut() {
        val currentZoom = binding.pdfView.zoom
        binding.pdfView.zoomTo(currentZoom * 0.8f)
    }

    private fun fitToWidth() {
        binding.pdfView.fitToWidth(currentPage)
    }

    private fun sharePdf() {
        if (!pdfPath.isNullOrEmpty()) {
            pdfUtils.sharePdf(File(pdfPath!!), bookTitle ?: "PDF Document")
        } else {
            Toast.makeText(this, "Cannot share PDF from URL", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up any temporary files
        pdfUtils.cleanup()
    }
}
