package co.metode.hamim.ebook

import android.app.ProgressDialog
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import co.metode.hamim.R
import co.metode.hamim.api.ApiClient
import co.metode.hamim.api.ApiInterface
import co.metode.hamim.databinding.ActivityBookDetailBinding
import co.metode.hamim.ebook.fragment.detail.JilidFragment
import co.metode.hamim.ebook.fragment.detail.ReviewFragment
import co.metode.hamim.ebook.fragment.detail.TentangFragment
import co.metode.hamim.ebook.model.EbookItem
import co.metode.hamim.ebook.util.DownloadManager
import com.bumptech.glide.Glide
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

/**
 * Activity untuk menampilkan detail buku, termasuk informasi, jilid, dan ulasan.
 */
class BookDetailActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBookDetailBinding
    private lateinit var preferencesManager: BookPreferencesManager
    private var isSaved = false
    private var book: Book? = null

    // Download manager untuk mengelola file ebook
    private lateinit var downloadManager: DownloadManager
    private var isDownloaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBookDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setStatusBarTransparent()

        preferencesManager = BookPreferencesManager(this)
        downloadManager = DownloadManager(this)

        val tabLayout: TabLayout = binding.tabLayout
        val viewPager: ViewPager2 = binding.viewPager

        val adapter = MyPagerAdapter(this)
        viewPager.adapter = adapter

        // Menghubungkan TabLayout dengan ViewPager2.
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "Tentang"
                1 -> "Jilid"
                2 -> "Review"
                else -> ""
            }
        }.attach()

        // Mengatur scroll ke atas saat tab dipilih.
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                Handler(Looper.getMainLooper()).postDelayed({
                    binding.nestedScrollView.smoothScrollTo(0, 0)
                }, 100)
            }
        })

        setupViews()
        loadBookData()
        loadSavedState()
        setupListeners()
    }

    /**
     * Adapter untuk ViewPager2 yang menampilkan fragment detail buku.
     */
    class MyPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {
        private val book: Book? = activity.intent.getParcelableExtra("book")

        override fun getItemCount(): Int = 3

        override fun createFragment(position: Int): androidx.fragment.app.Fragment {
            return when (position) {
                0 -> TentangFragment()
                1 -> JilidFragment()
                2 -> ReviewFragment()
                else -> TentangFragment()
            }
        }
    }

    /**
     * Mengatur tampilan dan listener awal.
     */
    private fun setupViews() {
        binding.ivBack.setOnClickListener { finish() }

        binding.btnSave.setOnClickListener {
            toggleSaveState()
        }

        binding.btnDownload.setOnClickListener {
            book?.let {
                if (it.isPurchased || it.isFree) {
                    downloadBook(it.id)
                } else {
                    Toast.makeText(this, "Please purchase the book first", Toast.LENGTH_SHORT).show()
                }
            }
        }

        binding.btnbBaca.setOnClickListener {
            book?.let {
                if (it.isPurchased || it.isFree) {
                    openBook(it.id)
                } else {
                    payForBook(it.id)
                }
            }
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }
    }

    /**
     * Memuat data refresh.
     */
    private fun loadData() {
        binding.swipeRefresh.isRefreshing = true

        Handler(Looper.getMainLooper()).postDelayed({
            binding.swipeRefresh.isRefreshing = false
        }, 2000)
    }

    /**
     * Memuat data buku dari Intent dan menampilkan di tampilan.
     */
    private fun loadBookData() {
        book = intent.getParcelableExtra("book")

        book?.let {
            binding.book = it

            val adapter = MyPagerAdapter(this)
            binding.viewPager.adapter = adapter

            // Memeriksa apakah buku sudah diunduh
            isDownloaded = downloadManager.isBookDownloaded(it.id, it.title)
            updateDownloadButtonState()

            // Mengatur tampilan berdasarkan status pembelian buku.
            if (it.isPurchased || it.isFree) {
                binding.btnSave.visibility = View.VISIBLE
                binding.btnbBaca.text = getString(R.string.read_now)
                binding.bookPrice.visibility = View.GONE
                binding.bookPriceTitle.visibility = View.GONE

                // Tampilkan tombol download hanya jika buku bisa diakses
                binding.btnDownload.visibility = View.VISIBLE

                // Update teks tombol download berdasarkan status unduhan
                updateDownloadButtonText()
            } else {
                binding.btnSave.visibility = View.GONE
                binding.btnbBaca.text = getString(R.string.beli_sekarang)
                binding.bookPrice.visibility = View.VISIBLE
                binding.bookPrice.text = "Rp250.000"
                binding.bookPriceTitle.visibility = View.VISIBLE
                binding.btnDownload.visibility = View.GONE
            }

            Glide.with(this)
                .load(it.coverUrl)
                .placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
                .into(binding.bookCover)
        }
    }

    /**
     * Memperbarui tampilan tombol download berdasarkan status unduhan
     */
    private fun updateDownloadButtonText() {
        if (isDownloaded) {
            // Tampilkan icon ceklis/downloaded untuk buku yang sudah diunduh
            binding.btnDownload.setImageResource(R.drawable.ic_downloaded)
            binding.btnDownload.contentDescription = getString(R.string.delete_book)

            // Tambahkan animasi kecil untuk menunjukkan status berubah
            binding.btnDownload.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(200)
                .withEndAction {
                    binding.btnDownload.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .start()
                }
                .start()
        } else {
            binding.btnDownload.setImageResource(R.drawable.ic_download)
            binding.btnDownload.contentDescription = getString(R.string.download_book)
        }
    }

    /**
     * Memperbarui status tombol download
     */
    private fun updateDownloadButtonState() {
        book?.let {
            isDownloaded = downloadManager.isBookDownloaded(it.id, it.title)
            updateDownloadButtonText()
        }
    }

    /**
     * Mengatur listener untuk tombol baca/beli.
     */
    private fun setupListeners() {
        binding.btnbBaca.setOnClickListener {
            book?.let {
                if (it.isPurchased || it.isFree) {
                    openBook(it.id)
                } else {
                    payForBook(it.id)
                }
            }
        }
    }

    /**
     * Menangani aksi membuka buku.
     * PDF viewer telah dihapus, gunakan aplikasi PDF viewer eksternal.
     */
    private fun openBook(bookId: String) {
        book?.let {
            // PDF viewer telah dihapus, gunakan PDF viewer eksternal
            Toast.makeText(this, "PDF viewer telah dihapus. Gunakan aplikasi PDF viewer eksternal untuk membuka buku.", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Menangani aksi mengunduh atau menghapus buku.
     * Jika buku belum diunduh, mengunduh dari API dan menyimpannya.
     * Jika buku sudah diunduh, menghapus file unduhan.
     */
    private fun downloadBook(bookId: String) {
        book?.let {
            // Jika buku sudah diunduh, hapus file
            if (isDownloaded) {
                val deleted = downloadManager.deleteDownloadedBook(bookId, it.title)
                if (deleted) {
                    Toast.makeText(this, "Buku berhasil dihapus", Toast.LENGTH_SHORT).show()
                    isDownloaded = false
                    updateDownloadButtonText()
                } else {
                    Toast.makeText(this, "Gagal menghapus buku", Toast.LENGTH_SHORT).show()
                }
                return@let
            }

            // Jika buku belum diunduh, unduh dari API
            val progressDialog = ProgressDialog(this).apply {
                setMessage("Mengunduh buku...")
                setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
                setCancelable(false)
                max = 100
                show()
            }

            // Use the dev client which automatically handles emulator vs physical device
            val apiInterface = ApiClient.getDevClient().create(ApiInterface::class.java)
            val call = apiInterface.downloadEbook(bookId)

            // Log the URL being used
            Log.d("BookDetailActivity", "Using URL: ${ApiClient.getDevUrl()} for downloading book $bookId")

            call.enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        // Dapatkan file tujuan dari DownloadManager
                        val pdfFile = downloadManager.getEbookFile(bookId, it.title)

                        // Simpan file yang diunduh
                        val saved = saveResponseBodyToFile(response.body(), pdfFile, progressDialog)
                        progressDialog.dismiss()

                        if (saved) {
                            // Simpan informasi buku ke database
                            book?.let { book ->
                                downloadManager.saveBookInfo(book)
                            }

                            // Tampilkan pesan sukses dengan animasi
                            val successMessage = "Buku berhasil diunduh"
                            val toast = Toast.makeText(this@BookDetailActivity, successMessage, Toast.LENGTH_SHORT)
                            toast.show()

                            // Update status dan tampilan
                            isDownloaded = true
                            updateDownloadButtonText()

                            // Tambahkan efek visual untuk menunjukkan buku sudah diunduh
                            binding.bookCover.animate()
                                .alpha(0.5f)
                                .setDuration(300)
                                .withEndAction {
                                    binding.bookCover.animate()
                                        .alpha(1.0f)
                                        .setDuration(300)
                                        .start()
                                }
                                .start()
                        } else {
                            Toast.makeText(this@BookDetailActivity, "Gagal mengunduh buku", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        progressDialog.dismiss()
                        Toast.makeText(this@BookDetailActivity, "Gagal mengunduh buku: ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    progressDialog.dismiss()
                    Log.e("BookDetailActivity", "Failed to download book", t)
                    Toast.makeText(this@BookDetailActivity, "Network error: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
        }
    }

    /**
     * Menyimpan ResponseBody ke file
     */
    private fun saveResponseBodyToFile(body: ResponseBody?, file: File, progressDialog: ProgressDialog): Boolean {
        if (body == null) return false

        try {
            var inputStream: InputStream? = null
            var outputStream: FileOutputStream? = null

            try {
                val fileReader = ByteArray(4096)
                val fileSize = body.contentLength()
                var fileSizeDownloaded: Long = 0

                inputStream = body.byteStream()
                outputStream = FileOutputStream(file)

                while (true) {
                    val read = inputStream.read(fileReader)
                    if (read == -1) break

                    outputStream.write(fileReader, 0, read)
                    fileSizeDownloaded += read

                    // Update progress
                    val progress = ((fileSizeDownloaded * 100) / fileSize).toInt()
                    progressDialog.progress = progress
                }

                outputStream.flush()
                return true
            } catch (e: IOException) {
                Log.e("BookDetailActivity", "Error saving PDF", e)
                return false
            } finally {
                inputStream?.close()
                outputStream?.close()
            }
        } catch (e: IOException) {
            Log.e("BookDetailActivity", "Error creating PDF file", e)
            return false
        }
    }



    /**
     * Menangani aksi pembayaran buku.
     */
    private fun payForBook(bookId: String) {
        Toast.makeText(this, "Pembayaran berhasil!", Toast.LENGTH_SHORT).show()

        book?.let {
            it.isPurchased = true
            binding.book = it
        }
    }

    /**
     * Memuat status penyimpanan buku dari SharedPreferences.
     */
    private fun loadSavedState() {
        book?.let {
            isSaved = preferencesManager.isBookSaved(it.id)
            updateSaveButtonState()
        }
    }

    /**
     * Mengubah status penyimpanan buku dan memperbarui tampilan tombol.
     */
    private fun toggleSaveState() {
        isSaved = !isSaved

        // Animasi tombol saat di-klik.
        binding.btnSave.animate()
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(100)
            .withEndAction {
                binding.btnSave.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(100)
                    .start()
            }
            .start()

        updateSaveButtonState()

        book?.let {
            preferencesManager.saveBook(it.id, isSaved)
        }

        showSaveMessage()
    }

    /**
     * Memperbarui tampilan tombol simpan berdasarkan status penyimpanan.
     */
    private fun updateSaveButtonState() {
        binding.btnSave.isSelected = isSaved
    }

    /**
     * Menampilkan pesan toast berdasarkan status penyimpanan.
     */
    private fun showSaveMessage() {
        val message = if (isSaved) "Buku berhasil disimpan" else "Buku dihapus dari simpanan"
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * Mengatur status bar transparan.
     */
    private fun setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = Color.TRANSPARENT
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            }
        }
    }
}