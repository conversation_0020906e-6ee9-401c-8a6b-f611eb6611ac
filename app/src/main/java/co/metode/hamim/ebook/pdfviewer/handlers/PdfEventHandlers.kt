package co.metode.hamim.ebook.pdfviewer.handlers

import android.content.Context
import android.graphics.Canvas
import android.util.Log
import android.view.MotionEvent
import android.widget.Toast
import com.github.barteksc.pdfviewer.listener.*

/**
 * Centralized event handlers for PDF viewer
 * Manages all PDF-related events and callbacks
 */
class PdfEventHandlers(private val context: Context) {

    companion object {
        private const val TAG = "PdfEventHandlers"
    }

    /**
     * Create load complete listener
     */
    fun createLoadCompleteListener(
        onSuccess: (Int) -> Unit,
        onError: ((Throwable) -> Unit)? = null
    ): OnLoadCompleteListener {
        return OnLoadCompleteListener { nbPages ->
            Log.d(TAG, "PDF loaded successfully with $nbPages pages")
            onSuccess(nbPages)
        }
    }

    /**
     * Create page change listener
     */
    fun createPageChangeListener(
        onPageChanged: (Int, Int) -> Unit
    ): OnPageChangeListener {
        return OnPageChangeListener { page, pageCount ->
            Log.d(TAG, "Page changed to $page of $pageCount")
            onPageChanged(page, pageCount)
        }
    }

    /**
     * Create page scroll listener
     */
    fun createPageScrollListener(
        onPageScrolled: (Int, Float) -> Unit
    ): OnPageScrollListener {
        return OnPageScrollListener { page, positionOffset ->
            onPageScrolled(page, positionOffset)
        }
    }

    /**
     * Create error listener
     */
    fun createErrorListener(
        onError: (Throwable) -> Unit
    ): OnErrorListener {
        return OnErrorListener { error ->
            Log.e(TAG, "PDF error occurred", error)
            onError(error)
        }
    }

    /**
     * Create page error listener
     */
    fun createPageErrorListener(
        onPageError: (Int, Throwable) -> Unit
    ): OnPageErrorListener {
        return OnPageErrorListener { page, error ->
            Log.e(TAG, "Error on page $page", error)
            onPageError(page, error)
        }
    }

    /**
     * Create render listener
     */
    fun createRenderListener(
        onInitialRender: (Int) -> Unit
    ): OnRenderListener {
        return OnRenderListener { nbPages ->
            Log.d(TAG, "PDF initially rendered with $nbPages pages")
            onInitialRender(nbPages)
        }
    }

    /**
     * Create tap listener
     */
    fun createTapListener(
        onTap: (MotionEvent) -> Boolean
    ): OnTapListener {
        return OnTapListener { event ->
            Log.d(TAG, "PDF tapped at (${event.x}, ${event.y})")
            onTap(event)
        }
    }

    /**
     * Create long press listener
     */
    fun createLongPressListener(
        onLongPress: (MotionEvent) -> Unit
    ): OnLongPressListener {
        return OnLongPressListener { event ->
            Log.d(TAG, "PDF long pressed at (${event.x}, ${event.y})")
            onLongPress(event)
        }
    }

    /**
     * Create draw listener for custom drawing on pages
     */
    fun createDrawListener(
        onDraw: (Canvas, Int, Int, Float, Float) -> Unit
    ): OnDrawListener {
        return OnDrawListener { canvas, pageWidth, pageHeight, displayedWidth, displayedHeight ->
            onDraw(canvas, pageWidth, pageHeight, displayedWidth, displayedHeight)
        }
    }

    /**
     * Create comprehensive event handler set
     */
    fun createComprehensiveHandlers(
        onLoad: (Int) -> Unit = {},
        onPageChange: (Int, Int) -> Unit = { _, _ -> },
        onPageScroll: (Int, Float) -> Unit = { _, _ -> },
        onError: (Throwable) -> Unit = { showDefaultError(it) },
        onPageError: (Int, Throwable) -> Unit = { page, error -> showDefaultPageError(page, error) },
        onRender: (Int) -> Unit = {},
        onTap: (MotionEvent) -> Boolean = { false },
        onLongPress: (MotionEvent) -> Unit = {},
        onDraw: (Canvas, Int, Int, Float, Float) -> Unit = { _, _, _, _, _ -> }
    ): PdfEventHandlerSet {
        return PdfEventHandlerSet(
            loadCompleteListener = createLoadCompleteListener(onLoad, onError),
            pageChangeListener = createPageChangeListener(onPageChange),
            pageScrollListener = createPageScrollListener(onPageScroll),
            errorListener = createErrorListener(onError),
            pageErrorListener = createPageErrorListener(onPageError),
            renderListener = createRenderListener(onRender),
            tapListener = createTapListener(onTap),
            longPressListener = createLongPressListener(onLongPress),
            drawListener = createDrawListener(onDraw)
        )
    }

    /**
     * Show default error message
     */
    private fun showDefaultError(error: Throwable) {
        val message = "PDF Error: ${error.message ?: "Unknown error"}"
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    /**
     * Show default page error message
     */
    private fun showDefaultPageError(page: Int, error: Throwable) {
        val message = "Error on page ${page + 1}: ${error.message ?: "Unknown error"}"
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * Data class to hold all event handlers
     */
    data class PdfEventHandlerSet(
        val loadCompleteListener: OnLoadCompleteListener,
        val pageChangeListener: OnPageChangeListener,
        val pageScrollListener: OnPageScrollListener,
        val errorListener: OnErrorListener,
        val pageErrorListener: OnPageErrorListener,
        val renderListener: OnRenderListener,
        val tapListener: OnTapListener,
        val longPressListener: OnLongPressListener,
        val drawListener: OnDrawListener
    )

    /**
     * Apply all handlers to a PDF configurator
     */
    fun applyHandlers(
        configurator: com.github.barteksc.pdfviewer.PDFView.Configurator,
        handlers: PdfEventHandlerSet
    ): com.github.barteksc.pdfviewer.PDFView.Configurator {
        return configurator
            .onLoad(handlers.loadCompleteListener)
            .onPageChange(handlers.pageChangeListener)
            .onPageScroll(handlers.pageScrollListener)
            .onError(handlers.errorListener)
            .onPageError(handlers.pageErrorListener)
            .onRender(handlers.renderListener)
            .onTap(handlers.tapListener)
            .onLongPress(handlers.longPressListener)
            .onDraw(handlers.drawListener)
    }

    /**
     * Create simple handlers for basic PDF viewing
     */
    fun createSimpleHandlers(
        onLoad: (Int) -> Unit,
        onPageChange: (Int, Int) -> Unit,
        onError: (Throwable) -> Unit
    ): PdfEventHandlerSet {
        return createComprehensiveHandlers(
            onLoad = onLoad,
            onPageChange = onPageChange,
            onError = onError
        )
    }
}
