package co.metode.hamim.ebook.fragment

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.metode.hamim.databinding.ActivityPaidBooksFragmentBinding
import co.metode.hamim.ebook.Book
import co.metode.hamim.ebook.BookActivity
import co.metode.hamim.ebook.BookAdapter

/**
 * Fragment untuk menampilkan daftar buku berbayar.
 */
class PaidBooksFragment : Fragment() {

    private var _binding: ActivityPaidBooksFragmentBinding? = null
    private val binding get() = _binding!!

    lateinit var recyclerView: RecyclerView
    private lateinit var bookAdapter: BookAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ActivityPaidBooksFragmentBinding.inflate(inflater, container, false)
        recyclerView = binding.rcPaidBooks
        return binding.root
    }

    // onViewCreated method moved to the bottom of the class

    /**
     * Mengatur RecyclerView dan adapter untuk menampilkan daftar buku berbayar.
     */
    private fun setupRecyclerView() {
        bookAdapter = BookAdapter(
            emptyList(),
            { book -> (requireActivity() as BookActivity).openDetailActivity(book) },
            requireContext()
        )
        recyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = bookAdapter
        }
    }

    /**
     * Mengatur daftar buku berbayar yang akan ditampilkan di RecyclerView.
     *
     * @param books Daftar buku yang akan ditampilkan.
     */
    fun setBooks(books: List<Book>) {
        Log.d("PaidBooksFragment", "setBooks called with: ${books.size} books")

        // Check if view is initialized
        if (_binding == null) {
            Log.d("PaidBooksFragment", "View not initialized yet, saving books for later")
            // Save the books for later when the view is created
            savedBooks = books
            return
        }

        val paidBooks = books.filter { !it.isFree }
        Log.d("PaidBooksFragment", "Filtered paid books: ${paidBooks.size}")

        try {
            // Immediately update UI without delay
            if (paidBooks.isEmpty()) {
                // Menampilkan pesan empty state jika daftar buku berbayar kosong.
                recyclerView.visibility = View.GONE
                binding.emptyStatePaid.visibility = View.VISIBLE
            } else {
                // Memperbarui adapter dan menampilkan RecyclerView jika daftar buku berbayar tidak kosong.
                recyclerView.visibility = View.VISIBLE
                bookAdapter.updateData(paidBooks)
                binding.emptyStatePaid.visibility = View.GONE
            }
        } catch (e: Exception) {
            Log.e("PaidBooksFragment", "Error updating UI: ${e.message}")
        }
    }

    // Store books for when view is created
    private var savedBooks: List<Book>? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()

        // If we have saved books, update the UI now
        savedBooks?.let {
            setBooks(it)
            savedBooks = null
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
