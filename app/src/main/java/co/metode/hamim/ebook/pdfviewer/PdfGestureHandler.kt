package co.metode.hamim.ebook.pdfviewer

import android.content.Context
import android.graphics.Matrix
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.view.GestureDetectorCompat

/**
 * Class for handling gestures and touch events in PDF viewer
 */
class PdfGestureHandler(
    context: Context,
    private val textSelectionManager: PdfTextSelectionManager,
    private val scale: Float
) {
    // Callback interface
    interface GestureCallback {
        fun onPageTap()
        fun onPageDoubleTap()
        fun onPageLongPress(x: Float, y: Float)
        fun onPageScroll(distanceX: Float, distanceY: Float)
        fun onPageFling(velocityX: Float, velocityY: Float)
    }

    private var callback: GestureCallback? = null
    private val gestureDetector: GestureDetectorCompat

    init {
        // Initialize gesture detector with custom settings
        val listener = object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                // Clear selection on single tap
                android.util.Log.d("PdfGestureHandler", "Single tap confirmed at x=${e.x}, y=${e.y}")

                // Cek apakah tap di area yang ada teksnya menggunakan PDFBox
                val hasTextAtPosition = textSelectionManager.hasTextAtPosition(e.x, e.y, scale)
                android.util.Log.d("PdfGestureHandler", "Has text at position: $hasTextAtPosition")

                // Selalu hapus seleksi saat single tap
                textSelectionManager.clearSelection()

                callback?.onPageTap()
                return true
            }

            override fun onDoubleTap(e: MotionEvent): Boolean {
                // Tidak lagi menggunakan double tap untuk seleksi teks
                android.util.Log.d("PdfGestureHandler", "Double tap detected at x=${e.x}, y=${e.y}")

                callback?.onPageDoubleTap()
                return true
            }

            override fun onLongPress(e: MotionEvent) {
                // Hanya gunakan long press untuk seleksi teks
                android.util.Log.d("PdfGestureHandler", "Long press detected at x=${e.x}, y=${e.y}")

                // Aktifkan seleksi teks pada long press
                selectWordAt(e.x, e.y)

                callback?.onPageLongPress(e.x, e.y)
            }

            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                callback?.onPageScroll(distanceX, distanceY)
                return true
            }

            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
            ): Boolean {
                callback?.onPageFling(velocityX, velocityY)
                return true
            }
        }

        // Buat gesture detector dengan listener yang sudah dibuat
        gestureDetector = GestureDetectorCompat(context, listener)

        // Konfigurasi gesture detector untuk mendeteksi double tap dengan lebih baik
        gestureDetector.setOnDoubleTapListener(listener)
        gestureDetector.setIsLongpressEnabled(true)
    }

    /**
     * Set the callback for gesture events
     */
    fun setCallback(callback: GestureCallback) {
        this.callback = callback
    }

    /**
     * Handle touch events for the PDF view
     */
    fun onTouchEvent(view: View, event: MotionEvent): Boolean {
        // Log untuk debugging
        android.util.Log.d("PdfGestureHandler", "onTouchEvent: action=${event.action}")

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // Let the gesture detector handle the event
                return gestureDetector.onTouchEvent(event)
            }
            MotionEvent.ACTION_MOVE -> {
                if (textSelectionManager.isInSelectionMode()) {
                    // Update text selection (already on main thread)
                    textSelectionManager.updateSelection(event.x, event.y, scale)
                    return true
                } else {
                    return gestureDetector.onTouchEvent(event)
                }
            }
            MotionEvent.ACTION_UP -> {
                // Let the gesture detector handle the event
                return gestureDetector.onTouchEvent(event)
            }
            else -> return gestureDetector.onTouchEvent(event)
        }
    }

    /**
     * Handle touch events for the start selection handle
     */
    fun onStartHandleTouchEvent(view: View, event: MotionEvent, imageView: View, matrix: Matrix): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                textSelectionManager.setStartHandleDragging(true)
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                if (textSelectionManager.isStartHandleDragging()) {
                    // Get the position in the PDF view coordinates
                    val values = FloatArray(9)
                    matrix.getValues(values)

                    // Convert screen coordinates to PDF coordinates
                    val translateX = values[Matrix.MTRANS_X]
                    val translateY = values[Matrix.MTRANS_Y]
                    val scaleX = values[Matrix.MSCALE_X]
                    val scaleY = values[Matrix.MSCALE_Y]

                    // Calculate PDF coordinates
                    val pdfX = (event.rawX - translateX) / scaleX
                    val pdfY = (event.rawY - translateY) / scaleY

                    // Find nearest position and update selection (already on main thread)
                    val index = textSelectionManager.findNearestPosition(pdfX, pdfY, scale)
                    android.util.Log.d("PdfGestureHandler", "Start handle moved to index: $index")

                    if (index >= 0) {
                        textSelectionManager.setSelection(
                            index,
                            textSelectionManager.getEndHandleIndex(),
                            scale
                        )
                    }
                    return true
                } else {
                    return false
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                textSelectionManager.setStartHandleDragging(false)
                return true
            }
            else -> return false
        }
    }

    /**
     * Calculate distance between two points
     */
    private fun calculateDistance(x1: Float, y1: Float, x2: Float, y2: Float): Float {
        val dx = x1 - x2
        val dy = y1 - y2
        return Math.sqrt((dx * dx + dy * dy).toDouble()).toFloat()
    }

    /**
     * Select word at the given coordinates
     */
    private fun selectWordAt(x: Float, y: Float) {
        try {
            // Mulai seleksi teks
            textSelectionManager.startSelection(x, y, scale)

            // Coba pilih kata lengkap
            val index = textSelectionManager.findNearestPosition(x, y, scale)
            android.util.Log.d("PdfGestureHandler", "selectWordAt: nearest index=$index")

            if (index >= 0) {
                val startWordIndex = textSelectionManager.findWordStart(index)
                val endWordIndex = textSelectionManager.findWordEnd(index)

                android.util.Log.d("PdfGestureHandler", "selectWordAt: word range=$startWordIndex to $endWordIndex")

                if (startWordIndex >= 0 && endWordIndex >= 0) {
                    textSelectionManager.setSelection(startWordIndex, endWordIndex, scale)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("PdfGestureHandler", "Error in selectWordAt", e)
        }
    }

    /**
     * Handle touch events for the end selection handle
     */
    fun onEndHandleTouchEvent(view: View, event: MotionEvent, imageView: View, matrix: Matrix): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                textSelectionManager.setEndHandleDragging(true)
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                if (textSelectionManager.isEndHandleDragging()) {
                    // Get the position in the PDF view coordinates
                    val values = FloatArray(9)
                    matrix.getValues(values)

                    // Convert screen coordinates to PDF coordinates
                    val translateX = values[Matrix.MTRANS_X]
                    val translateY = values[Matrix.MTRANS_Y]
                    val scaleX = values[Matrix.MSCALE_X]
                    val scaleY = values[Matrix.MSCALE_Y]

                    // Calculate PDF coordinates
                    val pdfX = (event.rawX - translateX) / scaleX
                    val pdfY = (event.rawY - translateY) / scaleY

                    // Find nearest position and update selection (already on main thread)
                    val index = textSelectionManager.findNearestPosition(pdfX, pdfY, scale)
                    android.util.Log.d("PdfGestureHandler", "End handle moved to index: $index")

                    if (index >= 0) {
                        textSelectionManager.setSelection(
                            textSelectionManager.getStartHandleIndex(),
                            index,
                            scale
                        )
                    }
                    return true
                } else {
                    return false
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                textSelectionManager.setEndHandleDragging(false)
                return true
            }
            else -> return false
        }
    }
}
