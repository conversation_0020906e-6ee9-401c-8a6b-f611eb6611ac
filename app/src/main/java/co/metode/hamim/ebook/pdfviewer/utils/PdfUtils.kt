package co.metode.hamim.ebook.pdfviewer.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import kotlinx.coroutines.*
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * Utility class for PDF operations
 * Handles file operations, downloads, and sharing
 */
class PdfUtils(private val context: Context) {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val tempFiles = mutableListOf<File>()
    
    companion object {
        private const val TAG = "PdfUtils"
        private const val TEMP_PDF_PREFIX = "temp_pdf_"
        private const val DOWNLOAD_TIMEOUT = 30L // seconds
    }

    /**
     * Download PDF from URL asynchronously
     */
    fun downloadPdfFromUrl(url: String, callback: (File?) -> Unit) {
        scope.launch {
            try {
                val downloadedFile = downloadPdfSync(url)
                withContext(Dispatchers.Main) {
                    callback(downloadedFile)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error downloading PDF from URL: $url", e)
                withContext(Dispatchers.Main) {
                    callback(null)
                }
            }
        }
    }

    /**
     * Download PDF synchronously (for use in coroutines)
     */
    private suspend fun downloadPdfSync(url: String): File? = withContext(Dispatchers.IO) {
        try {
            val client = OkHttpClient.Builder()
                .connectTimeout(DOWNLOAD_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DOWNLOAD_TIMEOUT, TimeUnit.SECONDS)
                .build()

            val request = Request.Builder()
                .url(url)
                .build()

            val response = client.newCall(request).execute()

            if (!response.isSuccessful) {
                Log.e(TAG, "Failed to download PDF: HTTP ${response.code}")
                return@withContext null
            }

            val inputStream = response.body?.byteStream()
            if (inputStream == null) {
                Log.e(TAG, "Response body is null")
                return@withContext null
            }

            val tempFile = createTempFile()
            if (saveInputStreamToFile(inputStream, tempFile)) {
                Log.d(TAG, "PDF downloaded successfully to: ${tempFile.absolutePath}")
                tempFiles.add(tempFile)
                tempFile
            } else {
                tempFile.delete()
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception during PDF download", e)
            null
        }
    }

    /**
     * Create a temporary file for PDF storage
     */
    private fun createTempFile(): File {
        val timestamp = System.currentTimeMillis()
        val fileName = "${TEMP_PDF_PREFIX}${timestamp}.pdf"
        return File(context.cacheDir, fileName)
    }

    /**
     * Save InputStream to file
     */
    private fun saveInputStreamToFile(inputStream: InputStream, file: File): Boolean {
        return try {
            FileOutputStream(file).use { outputStream ->
                inputStream.use { input ->
                    input.copyTo(outputStream)
                }
            }
            true
        } catch (e: IOException) {
            Log.e(TAG, "Error saving file: ${file.absolutePath}", e)
            false
        }
    }

    /**
     * Share PDF file using system share intent
     */
    fun sharePdf(file: File, title: String = "PDF Document") {
        try {
            if (!file.exists()) {
                Log.e(TAG, "Cannot share file that doesn't exist: ${file.absolutePath}")
                return
            }

            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "application/pdf"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, title)
                putExtra(Intent.EXTRA_TEXT, "Sharing PDF: $title")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Share PDF")
            chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(chooserIntent)

        } catch (e: Exception) {
            Log.e(TAG, "Error sharing PDF file", e)
        }
    }

    /**
     * Open PDF with external app
     */
    fun openWithExternalApp(file: File) {
        try {
            if (!file.exists()) {
                Log.e(TAG, "Cannot open file that doesn't exist: ${file.absolutePath}")
                return
            }

            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "application/pdf")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            context.startActivity(intent)

        } catch (e: Exception) {
            Log.e(TAG, "Error opening PDF with external app", e)
        }
    }

    /**
     * Get file size in human readable format
     */
    fun getFileSize(file: File): String {
        if (!file.exists()) return "0 B"
        
        val bytes = file.length()
        val unit = 1024
        if (bytes < unit) return "$bytes B"
        
        val exp = (Math.log(bytes.toDouble()) / Math.log(unit.toDouble())).toInt()
        val pre = "KMGTPE"[exp - 1]
        return String.format("%.1f %sB", bytes / Math.pow(unit.toDouble(), exp.toDouble()), pre)
    }

    /**
     * Check if file is a valid PDF
     */
    fun isValidPdf(file: File): Boolean {
        if (!file.exists() || !file.canRead()) return false
        
        return try {
            file.inputStream().use { input ->
                val header = ByteArray(4)
                val bytesRead = input.read(header)
                bytesRead == 4 && 
                header[0] == 0x25.toByte() && // %
                header[1] == 0x50.toByte() && // P
                header[2] == 0x44.toByte() && // D
                header[3] == 0x46.toByte()    // F
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking PDF validity", e)
            false
        }
    }

    /**
     * Get MIME type for file
     */
    fun getMimeType(file: File): String {
        val extension = file.extension.lowercase()
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "application/octet-stream"
    }

    /**
     * Copy file to app's private storage
     */
    fun copyToPrivateStorage(sourceFile: File, fileName: String): File? {
        return try {
            val destFile = File(context.filesDir, fileName)
            sourceFile.copyTo(destFile, overwrite = true)
            destFile
        } catch (e: Exception) {
            Log.e(TAG, "Error copying file to private storage", e)
            null
        }
    }

    /**
     * Clean up temporary files
     */
    fun cleanup() {
        scope.launch {
            tempFiles.forEach { file ->
                try {
                    if (file.exists()) {
                        file.delete()
                        Log.d(TAG, "Deleted temp file: ${file.absolutePath}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error deleting temp file: ${file.absolutePath}", e)
                }
            }
            tempFiles.clear()
        }
    }

    /**
     * Cancel all ongoing operations
     */
    fun cancelOperations() {
        scope.cancel()
    }
}
