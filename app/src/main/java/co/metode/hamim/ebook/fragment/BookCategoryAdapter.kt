package co.metode.hamim.ebook.fragment

import android.util.SparseArray
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * Adapter untuk ViewPager2 yang menampilkan fragment kategori buku.
 *
 * Adapter ini mengelola fragment untuk dua kategori: Buku Gratis dan B<PERSON>.
 * Menggunakan `SparseArray` untuk menyimpan dan mengelola fragment yang dibuat.
 */
class BookCategoryAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
    // Simpan fragment dalam map menggunakan SparseArray untuk efisiensi.
    private val fragments = SparseArray<Fragment>()

    init {
        // Pre-create fragments to ensure they're initialized
        fragments.put(0, FreeBooksFragment())
        fragments.put(1, PaidBooksFragment())
    }

    /**
     * Mengembalikan jumlah total kategori/fragment.
     *
     * @return Jumlah total kategori/fragment.
     */
    override fun getItemCount(): Int = 2

    /**
     * Membuat dan mengembalikan fragment pada posisi tertentu.
     * Fragment yang dibuat akan disimpan dalam `fragments` untuk penggunaan kembali.
     *
     * @param position Posisi fragment yang akan dibuat.
     * @return Fragment yang dibuat.
     * @throws IllegalArgumentException Jika posisi tidak valid.
     */
    override fun createFragment(position: Int): Fragment {
        // Return the pre-created fragment or create a new one if needed
        val existingFragment = fragments[position]
        if (existingFragment != null) {
            return existingFragment
        }

        // If fragment doesn't exist (shouldn't happen), create a new one
        return when (position) {
            0 -> FreeBooksFragment() // Tab "Gratis"
            1 -> PaidBooksFragment() // Tab "Berbayar"
            else -> throw IllegalArgumentException("Invalid position: $position")
        }.also { fragments.put(position, it) }
    }

    /**
     * Mengembalikan fragment yang disimpan pada posisi tertentu.
     *
     * @param position Posisi fragment yang akan dikembalikan.
     * @return Fragment yang disimpan, atau null jika tidak ada.
     */
    fun getFragment(position: Int): Fragment? = fragments[position]

    /**
     * Menyimpan fragment pada posisi tertentu.
     *
     * @param position Posisi fragment yang akan disimpan.
     * @param fragment Fragment yang akan disimpan.
     */
    fun setFragment(position: Int, fragment: Fragment) {
        fragments.put(position, fragment)
    }

    /**
     * Mengembalikan ID item pada posisi tertentu.
     *
     * @param position Posisi item.
     * @return ID item.
     */
    override fun getItemId(position: Int): Long = position.toLong()

    /**
     * Memeriksa apakah item dengan ID tertentu ada.
     *
     * @param itemId ID item yang akan diperiksa.
     * @return true jika item ada, false jika tidak.
     */
    override fun containsItem(itemId: Long): Boolean = itemId in 0 until itemCount
}
