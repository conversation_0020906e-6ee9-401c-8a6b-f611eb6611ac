package co.metode.hamim.ebook.pdfviewer

import android.app.AlertDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import android.widget.Toast
import co.metode.hamim.R

/**
 * Class for handling clipboard operations for PDF text
 */
class PdfClipboardManager(private val context: Context) {

    /**
     * Copy text to clipboard with improved formatting
     */
    fun copyToClipboard(text: String, showDialog: Boolean = true) {
        // Process the text to improve formatting
        val processedText = processTextForClipboard(text)

        // Copy to clipboard
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Selected Text", processedText)
        clipboard.setPrimaryClip(clip)

        // Show toast with preview of copied text
        val previewText = if (processedText.length > 30)
            "${processedText.substring(0, 30)}..."
        else
            processedText

        Toast.makeText(context, "Copied: $previewText", Toast.LENGTH_SHORT).show()

        // Also show a more detailed dialog with the copied text if requested
        if (showDialog) {
            showCopiedTextDialog(processedText)
        }
    }

    /**
     * Process text to improve formatting for clipboard
     */
    private fun processTextForClipboard(text: String): String {
        // Replace multiple spaces with a single space
        var result = text.replace(Regex("\\s+"), " ")

        // Trim leading/trailing whitespace
        result = result.trim()

        // Handle Arabic text - ensure proper RTL formatting
        if (containsArabic(result)) {
            // Add RTL mark at the beginning to ensure proper display
            result = "\u200F$result"
        }

        // Add line breaks between paragraphs if needed
        result = result.replace(". ", ".\n")

        return result
    }

    /**
     * Check if text contains Arabic characters
     */
    private fun containsArabic(text: String): Boolean {
        // Arabic Unicode block range
        val arabicRange = '\u0600'..'\u06FF'
        return text.any { it in arabicRange }
    }

    /**
     * Show dialog with the copied text for better visibility
     */
    private fun showCopiedTextDialog(text: String) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle(R.string.text_copied)

        // Create layout for dialog
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.dialog_copied_text, null)
        val textView = view.findViewById<TextView>(R.id.copiedTextView)

        // Set text
        textView.text = text

        // Set text direction based on content
        if (containsArabic(text)) {
            textView.textDirection = View.TEXT_DIRECTION_RTL
        } else {
            textView.textDirection = View.TEXT_DIRECTION_LTR
        }

        builder.setView(view)
        builder.setPositiveButton(R.string.ok, null)
        builder.setNeutralButton(R.string.copy_again) { _, _ ->
            // Copy again in case the first copy didn't work well
            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("Selected Text", text)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(context, R.string.text_copied_again, Toast.LENGTH_SHORT).show()
        }
        builder.show()
    }
}
