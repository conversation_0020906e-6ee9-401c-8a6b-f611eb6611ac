package co.metode.hamim.api;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

/**
 * Configuration class for API URLs and settings
 */
public class ApiConfig {
    // Main API URLs
    public static final String BASE_OLD_URL = "https://api.maqdisacademy.com/";
    public static final String BASE_URL = "https://apihamimv2.maqdisacademy.com/";
    public static final String ALADHAN_URL = "https://api.aladhan.com/v1/";
    
    // Ebook API URL - this should be your production URL when ready
    // For now, we'll use the same base URL as the main API
    public static final String EBOOK_API_URL = "https://apihamimv2.maqdisacademy.com/";
    
    // Flag to determine if we're in debug mode
    private static boolean isDebugMode = false;
    
    /**
     * Initialize the API configuration
     * @param context Application context
     */
    public static void init(Context context) {
        // Check if app is in debug mode
        isDebugMode = (0 != (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE));
        
        Log.d("ApiConfig", "App is in " + (isDebugMode ? "debug" : "release") + " mode");
        Log.d("ApiConfig", "Using Ebook API URL: " + getEbookApiUrl());
    }
    
    /**
     * Get the appropriate URL for the ebook API based on build type
     * @return The URL to use for ebook API calls
     */
    public static String getEbookApiUrl() {
        if (isDebugMode) {
            // In debug mode, use the development URL that automatically
            // handles emulator vs physical device
            return ApiClient.getDevUrl();
        } else {
            // In release mode, use the production URL
            return EBOOK_API_URL;
        }
    }
    
    /**
     * Get a unique device identifier for API calls
     * @param context Application context
     * @return A unique device identifier
     */
    public static String getDeviceId(Context context) {
        return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
    }
    
    /**
     * Get device information for debugging
     * @return A string with device information
     */
    public static String getDeviceInfo() {
        return "Model: " + Build.MODEL + 
               ", Android: " + Build.VERSION.RELEASE + 
               ", SDK: " + Build.VERSION.SDK_INT;
    }
}
