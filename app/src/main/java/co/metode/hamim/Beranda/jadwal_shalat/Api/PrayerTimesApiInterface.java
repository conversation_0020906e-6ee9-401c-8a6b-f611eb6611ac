package co.metode.hamim.Beranda.jadwal_shalat.Api;

import co.metode.hamim.Beranda.jadwal_shalat.model.WaktuSholat;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface PrayerTimesApiInterface {
    @GET("timings/{date}")
    Call<WaktuSholat> getPrayerTimes(
            @Path("date") String date,
            @Query("latitude") double latitude,
            @Query("longitude") double longitude,
            @Query("method") int method,
            @Query("school") String school
    );
}