package co.metode.hamim.Beranda.jadwal_shalat.handler;

import java.text.SimpleDateFormat;
import java.util.Date;

import co.metode.hamim.Beranda.jadwal_shalat.Api.PrayerTimesApiInterface;
import co.metode.hamim.Beranda.jadwal_shalat.model.WaktuSholat;
import co.metode.hamim.api.ApiClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

public class WaktuSholatHandler  {
    private static WaktuSholatHandler instance;
    private PrayerTimesApiInterface apiInterface;

    private WaktuSholatHandler() {
        Retrofit retrofit = ApiClient.getPrayerTimes();
        apiInterface = retrofit.create(PrayerTimesApiInterface.class);
    }

    public static synchronized WaktuSholatHandler getInstance() {
        if (instance == null) {
            instance = new WaktuSholatHandler();
        }
        return instance;
    }

    public void getPrayerTimes(double latitude, double longitude,
                               PrayerTimesCallback callback) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        String date = dateFormat.format(new Date());

        apiInterface.getPrayerTimes(date, latitude, longitude, 2, "shafi")
                .enqueue(new Callback<WaktuSholat>() {
                    @Override
                    public void onResponse(Call<WaktuSholat> call,
                                           Response<WaktuSholat> response) {
                        if (response.isSuccessful() && response.body() != null
                                && response.body().data != null) {
                            callback.onSuccess(response.body().data.timings);
                        } else {
                            callback.onError("Failed to get prayer times");
                        }
                    }

                    @Override
                    public void onFailure(Call<WaktuSholat> call, Throwable t) {
                        callback.onError(t.getMessage());
                    }
                });
    }

    // Callback interface
    public interface PrayerTimesCallback {
        void onSuccess(WaktuSholat.Timings timings);
        void onError(String message);
    }
}