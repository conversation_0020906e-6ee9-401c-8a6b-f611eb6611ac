package co.metode.hamim.allMenuBeranda.CekBacaan;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.Manifest;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.ContextWrapper;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaPlayer;
import android.media.MediaRecorder;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.firebase.messaging.FirebaseMessaging;

import java.io.File;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import co.metode.hamim.Audio;
import co.metode.hamim.DetailEcourse;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.add_transaksi.AddTransaksi;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.get_saldo.Saldo;
import co.metode.hamim.payment.Checkout;
import co.metode.hamim.topup.topUp;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class RekamBacaan extends AppCompatActivity implements View.OnClickListener {
    TextView text_menu,namasurat,text_waktu,tpoin;
    ImageView btn_rekam, btn_henti_rekam;
    ConstraintLayout back;
    MediaRecorder mediaRecorder;
    private static int MICROPHONE_PEEMISSION_CODE = 200;
    private Handler handler = new Handler();
    private BottomSheetDialog bottomSheetDialog;
    private SessionManager sessionManager;
    private String id_user, id_juz, id_surat,id_detail_surat,tgl, nama_rekam,namaSurat;
    private Dialog dialog,dialog1;
    LinearLayout btn_poin;
    ImageView btnPlay;
    MediaPlayer mediaPlayer;
    TextView totalduration_myseek;
    ProgressDialog pd;
    LinearLayout record_sudahAdaSetoran, record_layoutUst;
    RelativeLayout record_belumAdaSetoran, record_layoutBelumAda;
    boolean recordust = false;

//    stopwatch
    int Seconds, Minutes, MilliSeconds,poin,harga_poin ;
    long MillisecondTime, StartTime, TimeBuff, UpdateTime = 0L ;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_rekam_bacaan);
        setStatusBarTransparent();

        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        btn_rekam = findViewById(R.id.btn_rekam);
        btn_henti_rekam = findViewById(R.id.btn_henti_rekam);
        namasurat = findViewById(R.id.namasurat);
        text_waktu = findViewById(R.id.text_waktu);
        btn_poin = findViewById(R.id.btn_poin);
        tpoin = findViewById(R.id.tpoin);
        bottomSheetDialog = new BottomSheetDialog(RekamBacaan.this, R.style.AppBottomSheetDialogTheme);
        bottomSheetDialog.setCanceledOnTouchOutside(false);
        sessionManager = new SessionManager(RekamBacaan.this);
        DateFormat dateFormat = new SimpleDateFormat("yymmdd");
        Date date = new Date();
        mediaPlayer = new MediaPlayer();
        dialog = new Dialog(RekamBacaan.this);


        //get
        namaSurat = getIntent().getStringExtra("nama_surat");
        id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);
        id_juz = getIntent().getStringExtra("id_juz");
        id_detail_surat = getIntent().getStringExtra("id_detail_surat");
        id_surat = getIntent().getStringExtra("id_surat");
        harga_poin = Integer.parseInt(getIntent().getStringExtra("harga_poin"));
        tgl = dateFormat.format(date);


        //set
        bottomSheetDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                MillisecondTime = 0L ;
                StartTime = 0L ;
                TimeBuff = 0L ;
                UpdateTime = 0L ;
                Seconds = 0 ;
                Minutes = 0 ;
                MilliSeconds = 0 ;

                text_waktu.setText("00:00:00");
                mediaPlayer.reset();
            }
        });
        nama_rekam = id_juz+id_surat+id_detail_surat+id_user+System.currentTimeMillis();
        text_menu.setText("");
        namasurat.setText(namaSurat);
        if (isMicrophonePresent()) {
            getMicrophonePermission();
        }
        cek_audio();
        on_click();

        //get
        get_poin();
    }

    private void get_poin() {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<Saldo> call = apiInterface.saldoRespons(id_user);
        call.enqueue(new Callback<Saldo>() {
            @Override
            public void onResponse(Call<Saldo> call, Response<Saldo> response) {
                if (response.isSuccessful()){
                    int saldo = response.body().getSaldo();
                    poin = saldo;
                    DecimalFormat decimalFormat = new DecimalFormat("###,###,###");
                    tpoin.setText(decimalFormat.format(saldo));
                    if (saldo <= harga_poin){
                        dialog_kurang();
                    }
                }
            }

            @Override
            public void onFailure(Call<Saldo> call, Throwable t) {
            }
        });
    }

    private void cek_audio() {
        File file = new File(getRecordingFilePath());

        if (file.exists()){
            open_dialog();
        }
    }

    private void on_click() {
        back.setOnClickListener(this);
        btn_rekam.setOnClickListener(this);
        btn_henti_rekam.setOnClickListener(this);
        btn_poin.setOnClickListener(this);
    }


    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.back:
                onBackPressed();
                break;
            case R.id.btn_rekam:
                if (poin <= harga_poin){
                    dialog_kurang();
                }else{
                    btnRecordPress();
                }
                break;
            case R.id.btn_henti_rekam:
                btnStopPress();
                break;
            case R.id.btn_poin:
                Intent goToTopUp = new Intent(RekamBacaan.this, topUp.class);
                startActivity(goToTopUp);
                break;
        }
    }

    public Runnable run = new Runnable() {

        public void run() {

            MillisecondTime = SystemClock.uptimeMillis() - StartTime;

            UpdateTime = TimeBuff + MillisecondTime;

            Seconds = (int) (UpdateTime / 1000);

            Minutes = Seconds / 60;

            Seconds = Seconds % 60;

            MilliSeconds = (int) (UpdateTime % 1000);

            text_waktu.setText("" + Minutes + ":"
                    + String.format("%02d", Seconds) + ":"
                    + String.format("%03d", MilliSeconds));

            handler.postDelayed(this, 0);
        }

    };

    private boolean isMicrophonePresent() {
        if (this.getPackageManager().hasSystemFeature(PackageManager.FEATURE_MICROPHONE)) {
            return true;
        } else {
            return false;
        }
    }

    private void getMicrophonePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                == PackageManager.PERMISSION_DENIED) {
            ActivityCompat.requestPermissions(this, new String[]
                    {Manifest.permission.RECORD_AUDIO}, MICROPHONE_PEEMISSION_CODE);
        }
    }

    private String getRecordingFilePath() {
        ContextWrapper contextWrapper = new ContextWrapper(getApplicationContext());
        File musicDirectory = contextWrapper.getExternalFilesDir(Environment.DIRECTORY_MUSIC);
        File file = new File(musicDirectory, nama_rekam + ".mp3");
        return file.getPath();
    }

    public void btnRecordPress() {
        StartTime = SystemClock.uptimeMillis();
        handler.postDelayed(run, 0);


        try {
            mediaRecorder = new MediaRecorder();
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);
            mediaRecorder.setOutputFile(getRecordingFilePath());
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
            mediaRecorder.prepare();
            mediaRecorder.start();

            Toast.makeText(this, "Memulai Rekaman", Toast.LENGTH_SHORT).show();
            btn_rekam.setVisibility(View.GONE);
            btn_henti_rekam.setVisibility(View.VISIBLE);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void btnStopPress(){
        TimeBuff += MillisecondTime;

        handler.removeCallbacks(run);

        mediaRecorder.stop();
        mediaRecorder.release();
        mediaRecorder = null;

        Toast.makeText(this, "Rekaman berhenti", Toast.LENGTH_SHORT).show();
        btn_henti_rekam.setVisibility(View.GONE);
        btn_rekam.setVisibility(View.VISIBLE);
        open_dialog();
    }

    private void open_dialog() {
        bottomSheetDialog.setContentView(R.layout.bottom_sheet_rekaman);
        ImageView play_cek_bacaan = bottomSheetDialog.findViewById(R.id.play_cek_bacaan);
        AppCompatButton kirim_rekaman_cek_bacaan = bottomSheetDialog.findViewById(R.id.kirim_rekaman_cek_bacaan);
        TextView buang_rekaman_cek_bacaan = bottomSheetDialog.findViewById(R.id.buang_rekaman_cek_bacaan);
        ImageView close_player_cek_bacaan = bottomSheetDialog.findViewById(R.id.close_player_cek_bacaan);

        //player
        preparedMediaPlayer(bottomSheetDialog);
        updateSeekBar(bottomSheetDialog);

        //Onclick
        play_cek_bacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                play(bottomSheetDialog);
            }
        });
        close_player_cek_bacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheetDialog.dismiss();
            }
        });
        buang_rekaman_cek_bacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hapus_rekaman();

            }
        });
        kirim_rekaman_cek_bacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (poin <= harga_poin){
                    dialog_kurang();
                }else {
                    dialog_bayar();
                }
            }
        });

        bottomSheetDialog.show();
    }

    private void hapus_rekaman() {
        File file = new File(getRecordingFilePath());
        boolean hapus = file.delete();
        if(hapus){
            bottomSheetDialog.dismiss();
            Toast.makeText(this, "Buang rekaman berhasil", Toast.LENGTH_SHORT).show();
        }else {
            Toast.makeText(this, "Buang rekaman gagal", Toast.LENGTH_SHORT).show();
        }
    }

    private void dialog_kurang() {
        dialog.setContentView(R.layout.dialog_poin_kurang);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        AppCompatButton btnIsiPoin = dialog.findViewById(R.id.btnIsiPoin);
        ImageView cancel = dialog.findViewById(R.id.cancel);
        TextView message = dialog.findViewById(R.id.message);


        message.setText("Poinmu tidak mencukupi");
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        btnIsiPoin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent goToTopUp = new Intent(RekamBacaan.this, topUp.class);
                startActivity(goToTopUp);
            }
        });

        dialog.show();
    }

    private void dialog_bayar() {
        dialog.setContentView(R.layout.dialog_poin_kurang);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        AppCompatButton btnIsiPoin = dialog.findViewById(R.id.btnIsiPoin);
        ImageView cancel = dialog.findViewById(R.id.cancel);
        TextView message = dialog.findViewById(R.id.message);


        message.setText("Poinmu akan terpotong senilai "+harga_poin);
        btnIsiPoin.setText("Lanjutkan");
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        btnIsiPoin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                upload_rekaman();
            }
        });

        dialog.show();
    }

    private void kurangi_poin() {
        String id_order = "STR"+System.currentTimeMillis();
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener<String>() {
            @Override
            public void onComplete(@NonNull Task<String> task) {
                String token = task.getResult();
                ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                Call<AddTransaksi> call = apiInterface.kurangiPoin(id_order, id_user, "Setoran", harga_poin, token);
                call.enqueue(new Callback<AddTransaksi>() {
                    @Override
                    public void onResponse(Call<AddTransaksi> call, Response<AddTransaksi> response) {
                        if (response.isSuccessful()){
                            dialog1.dismiss();
                            dialog.dismiss();
                            bottomSheetDialog.dismiss();
                            if (response.body().getMessage().equals("berhasil")){
                                Toast.makeText(RekamBacaan.this, "Poin berhasil dikurangi", Toast.LENGTH_SHORT).show();
                            }else {
                                Toast.makeText(RekamBacaan.this, response.body().getMessage(), Toast.LENGTH_SHORT).show();
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<AddTransaksi> call, Throwable t) {
                    }
                });
            }
        });
    }

    private void upload_rekaman() {
        dialog1 = new Dialog(this, R.style.AppBottomSheetDialogTheme);
        dialog1.setContentView(R.layout.loading_data);
        dialog1.show();
        ContextWrapper contextWrapper = new ContextWrapper(getApplicationContext());
        File musicDirectory = contextWrapper.getExternalFilesDir(Environment.DIRECTORY_MUSIC);
        File file = new File(musicDirectory, nama_rekam + ".mp3");

        RequestBody requestBody = RequestBody.create(MediaType.parse("*/*"), file);
        MultipartBody.Part fileToUpload = MultipartBody.Part.createFormData("file", file.getName(), requestBody);
        RequestBody filename, idUser,idJuz,idSurat,idDetailSurat;
        filename = RequestBody.create(MediaType.parse("text/plain"), file.getName());
        idUser = RequestBody.create(MediaType.parse("text/plain"), id_user);
        idJuz = RequestBody.create(MediaType.parse("text/plain"), id_juz);
        idSurat = RequestBody.create(MediaType.parse("text/plain"), id_surat);
        idDetailSurat = RequestBody.create(MediaType.parse("text/plain"), id_detail_surat);


        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener<String>() {
            @Override
            public void onComplete(@NonNull Task<String> task) {
                String token_str = task.getResult();
                RequestBody token = RequestBody.create(MediaType.parse("text/plain"), token_str);
                ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                Call<DataUploadRekaman> call = apiInterface.uploadRekaman(fileToUpload, filename, idUser, idJuz, idSurat, idDetailSurat,token);
                call.enqueue(new Callback<DataUploadRekaman>() {
                    @Override
                    public void onResponse(Call<DataUploadRekaman> call, Response<DataUploadRekaman> response) {
                        if (response.isSuccessful()) {
                            if (response.body().getStatus().equals("berhasil")) {
                                kurangi_poin();
                                hapus_rekaman();
                                Intent intent = new Intent(RekamBacaan.this, DetailCekBacaan.class);
                                intent.putExtra("id_detail_surat", id_detail_surat);
                                intent.putExtra("nama_surat", namaSurat);
                                intent.putExtra("harga_poin", poin);
                                intent.putExtra("parameter", "1");
                                startActivity(intent);
                                finish();
                            }
                            Toast.makeText(RekamBacaan.this, response.body().getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(Call<DataUploadRekaman> call, Throwable t) {
                        Log.e("API_ERROR", "Request failed: " + t.getLocalizedMessage(), t);                    }
                });
            }
        });
    }

    private void preparedMediaPlayer(BottomSheetDialog bottomSheetDialog){
        SeekBar seek_cek_bacaan = bottomSheetDialog.findViewById(R.id.seek_cek_bacaan);
        ImageView play_cek_bacaan = bottomSheetDialog.findViewById(R.id.play_cek_bacaan);

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    mediaPlayer.setDataSource(getRecordingFilePath());
                    mediaPlayer.prepare();
                    updateSeekBar(bottomSheetDialog);
                    pd.dismiss();
                }catch (Exception exception){
                    exception.printStackTrace();
                }
            }
        },1000);


        seek_cek_bacaan.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                SeekBar seekBar = (SeekBar) v;
                int playPosition = (mediaPlayer.getDuration() / 100) * seekBar.getProgress();
                mediaPlayer.seekTo(playPosition);
                return false;
            }
        });

        mediaPlayer.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
            @Override
            public void onBufferingUpdate(MediaPlayer mediaPlayer, int i) {
                seek_cek_bacaan.setSecondaryProgress(i);
            }
        });
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                seek_cek_bacaan.setProgress(0);
                play_cek_bacaan.setImageResource(R.drawable.ic_play_cek_bacaan);
            }
        });
    }

    private Runnable updater = new Runnable() {
        @Override
        public void run() {
            updateSeekBar(bottomSheetDialog);
            long currentDuration = mediaPlayer.getCurrentPosition();
        }
    };

    private void updateSeekBar(BottomSheetDialog bottomSheetDialog){
        if (mediaPlayer.isPlaying()){
            SeekBar seek_cek_bacaan = bottomSheetDialog.findViewById(R.id.seek_cek_bacaan);
            seek_cek_bacaan.setProgress((int) (((float) mediaPlayer.getCurrentPosition() / mediaPlayer.getDuration()) * 100));
            handler.postDelayed(updater, 1000);
        }
    }

    private void play(BottomSheetDialog bottomSheetDialog) {
        ImageView play_cek_bacaan = bottomSheetDialog.findViewById(R.id.play_cek_bacaan);
        if (mediaPlayer.isPlaying()){
            handler.removeCallbacks(updater);
            mediaPlayer.pause();
            play_cek_bacaan.setImageResource(R.drawable.ic_play_cek_bacaan);
        }else {
            mediaPlayer.start();
            play_cek_bacaan.setImageResource(R.drawable.ic_pause_cek_bacaan);
            updateSeekBar(bottomSheetDialog);
        }
    }

    private String milliSecondsToTimer(long milliSeconds){
        String timerString = "";
        String secondsString;

        int hours = (int) (milliSeconds / (1000 * 60 * 60));
        int minutes = (int) (milliSeconds % (1000 * 60 * 60)) / (1000 * 60);
        int seconds = (int) (milliSeconds % (1000 * 60 * 60)) % (1000 * 60) / 1000;

        if(hours > 0){
            timerString = hours + ":";
        }
        if (seconds < 10){
            secondsString = "0" + seconds;
        }else {
            secondsString = "" + seconds;
        }

        timerString = timerString + minutes + ":" + secondsString;
        return timerString;

    }
}