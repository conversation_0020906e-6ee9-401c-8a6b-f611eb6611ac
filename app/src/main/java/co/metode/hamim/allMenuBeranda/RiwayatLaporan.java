package co.metode.hamim.allMenuBeranda;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.Adapter;
import co.metode.hamim.Audio;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import co.metode.hamim.data_item.hubungikami.AdapterGetLaporan;
import co.metode.hamim.data_item.hubungikami.GetLaporankuItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class RiwayatLaporan extends AppCompatActivity {
    RecyclerView rcLaporanku;
    ApiInterface apiInterface;
    SessionManager sessionManager;
    TextView belumadaLaporan;
    AdapterGetLaporan adapterGetLaporan;
    LinearLayoutManager linearLayoutManager;
    TextView text_menu;
    ConstraintLayout back;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_riwayat_laporan);
        setStatusBarTransparent();

        rcLaporanku = findViewById(R.id.rcLaporanku);
        belumadaLaporan = findViewById(R.id.belumadaLaporan);
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);

        sessionManager = new SessionManager(RiwayatLaporan.this);
        apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        linearLayoutManager = new LinearLayoutManager(RiwayatLaporan.this);
        rcLaporanku.setLayoutManager(linearLayoutManager);

        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        text_menu.setText("Riwayat Laporan");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        getLaporan(id_user);

    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void getLaporan(String id_user) {
        Call<List<GetLaporankuItem>> call = apiInterface.getLaporan(id_user);
        call.enqueue(new Callback<List<GetLaporankuItem>>() {
            @Override
            public void onResponse(Call<List<GetLaporankuItem>> call, Response<List<GetLaporankuItem>> response) {
                if (response.isSuccessful() && !response.body().isEmpty()){
                    List<GetLaporankuItem> posts = response.body();
                    adapterGetLaporan = new AdapterGetLaporan(RiwayatLaporan.this, posts);
                    rcLaporanku.setAdapter(adapterGetLaporan);
                }else {
                    rcLaporanku.setVisibility(View.GONE);
                    belumadaLaporan.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(Call<List<GetLaporankuItem>> call, Throwable t) {
                Log.e("API_ERROR", "Request failed: " + t.getLocalizedMessage(), t);            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }
}