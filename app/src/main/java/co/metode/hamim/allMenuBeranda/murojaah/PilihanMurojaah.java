package co.metode.hamim.allMenuBeranda.murojaah;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;

import co.metode.hamim.R;

public class PilihanMurojaah extends AppCompatActivity {
    LinearLayout btnGabunganSurat, btnSambungAyat;
    TextView text_menu;
    ConstraintLayout back;
    private String id_juz,img;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pilihan_murojaah);
        setStatusBarTransparent();

        //inisialisasi
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        btnGabunganSurat = findViewById(R.id.btnGabunganSurat);
        btnSambungAyat = findViewById(R.id.btnSambungAyat);
        id_juz = getIntent().getStringExtra("id_juz");
        img = getIntent().getStringExtra("img");

        //set
    // Tambahkan log untuk memeriksa text_menu
        if (text_menu != null) {
            Log.d("PilihanMurojaah", "TextView text_menu ditemukan!");
            text_menu.setText("Hafalan Yuk!");
        } else {
            Log.e("PilihanMurojaah", "TextView text_menu tidak ditemukan!");
        }

//        back.setColorFilter(getResources().getColor(R.color.color_V2_3));
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        btnGabunganSurat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(PilihanMurojaah.this, GabunganSurat.class);
                intent.putExtra("id_juz", id_juz);
                intent.putExtra("img",img);
                startActivity(intent);
            }
        });

        btnSambungAyat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(PilihanMurojaah.this, SambungAyat.class);
                intent.putExtra("id_juz", id_juz);
                startActivity(intent);
            }
        });


    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }
}