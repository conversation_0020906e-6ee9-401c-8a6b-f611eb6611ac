package co.metode.hamim.allMenuBeranda.CekBacaan;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.murojaah.PlayMurojaah;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import co.metode.hamim.detail_surat.DataItem;
import co.metode.hamim.juz.JuzItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterDaftarSuratCekBacaan extends RecyclerView.Adapter<AdapterDaftarSuratCekBacaan.AdapterHolder> {
    private Context context;
    private List<DataItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterDaftarSuratCekBacaan(Context context, List<DataItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }
    @NonNull
    @Override
    public AdapterDaftarSuratCekBacaan.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_daftar_surat,parent,false);
        AdapterDaftarSuratCekBacaan.AdapterHolder holder = new AdapterDaftarSuratCekBacaan.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterDaftarSuratCekBacaan.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        int nomor = position + 1;

        final DataItem juzItem = dataList.get(position);
        String id_potongan = juzItem.getIdDetailSurat();
        String namaSurat = ((Activity)context).getIntent().getStringExtra("nama_surat");
        String id_surat = ((Activity)context).getIntent().getStringExtra("id_surat");
        String id_juz = ((Activity)context).getIntent().getStringExtra("id_juz");
        String diskon_poin = ((Activity)context).getIntent().getStringExtra("diskon_poin");

        String poin = juzItem.getPoin();
        String potongan = juzItem.getJumlahAyat();
        String nama_surat = namaSurat+" ("+potongan+")";
        String cacheharga = poin;

        if (diskon_poin.equals("0")){
            diskon_poin = "0";
        }
        int kurangi = Integer.parseInt(poin) - Integer.parseInt(diskon_poin);
        int finalPoin = kurangi;

        if (diskon_poin.equals("0")){
            holder.harga_coret_poin.setVisibility(View.GONE);
            holder.harga_poin.setText(String.valueOf(finalPoin));
        }else {
            holder.harga_coret_poin.setText(cacheharga);
            holder.harga_coret_poin.setPaintFlags(holder.harga_coret_poin.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
            holder.harga_poin.setText(String.valueOf(finalPoin));
        }

        holder.no_detail_surat.setText(String.valueOf(nomor));
        holder.judul_detail_surat.setText(namaSurat+" ("+potongan+")");


        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<DataCekStatus> call = apiInterface.cekStatus(id_user,id_potongan);
        call.enqueue(new Callback<DataCekStatus>() {
            @Override
            public void onResponse(Call<DataCekStatus> call, Response<DataCekStatus> response) {
                if (response.isSuccessful()){
                    String tgl_kirim = response.body().getTglKirim();
                    String status = response.body().getStatus();
                    String audio_user = response.body().getAudioUser();
                    String audio_ust = response.body().getAudioUst();
                    String catatan = response.body().getCatatan();
                    String id_setoran = response.body().getIdSetoran();


                    if (response.body().getMessage().equals("berhasil")){
                        int drawable;
                        String text_status;
                        if (response.body().getStatus().equals("1")){
                            drawable = R.drawable.ic_menunggu;
                            text_status = "Menunggu";
                        }else if (response.body().getStatus().equals("2")){
                            drawable = R.drawable.ic_pemula;
                            text_status = "Pemula";
                        }else if (response.body().getStatus().equals("3")){
                            drawable = R.drawable.ic_terampil;
                            text_status = "Terampil";
                        }else {
                            drawable = R.drawable.ic_mahir;
                            text_status = "Mahir";
                        }
                        holder.layoutStatus.setVisibility(View.VISIBLE);
                        holder.ic_status.setImageResource(drawable);
                        holder.text_status.setText(text_status);
                        holder.text_aksi.setVisibility(View.GONE);
                        holder.ic_aksi.setImageResource(R.drawable.right_ic);
                        holder.ic_aksi.setColorFilter(ContextCompat.getColor(context, R.color.menunggu), android.graphics.PorterDuff.Mode.MULTIPLY);
                        holder.data.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Intent intent = new Intent(context, DetailCekBacaan.class);
                                intent.putExtra("id_detail_surat", id_potongan);
                                intent.putExtra("id_setoran", id_setoran);
                                intent.putExtra("id_surat", id_surat);
                                intent.putExtra("id_juz", id_juz);
                                intent.putExtra("nama_surat", nama_surat);
                                intent.putExtra("tgl_kirim", tgl_kirim);
                                intent.putExtra("status", status);
                                intent.putExtra("audio_user", audio_user);
                                intent.putExtra("audio_ust", audio_ust);
                                intent.putExtra("catatan", catatan);
                                intent.putExtra("poin", String.valueOf(finalPoin));
                                intent.putExtra("parameter", "0");
                                context.startActivity(intent);
                            }
                        });
                    }else {
                        holder.layoutStatus.setVisibility(View.GONE);
                        holder.rekam.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Intent intent = new Intent(context, RekamBacaan.class);
                                intent.putExtra("id_detail_surat", id_potongan);
                                intent.putExtra("id_surat", id_surat);
                                intent.putExtra("id_juz", id_juz);
                                intent.putExtra("nama_surat", nama_surat);
                                intent.putExtra("harga_poin", String.valueOf(finalPoin));
                                context.startActivity(intent);
                            }
                        });
                    }
                }
            }

            @Override
            public void onFailure(Call<DataCekStatus> call, Throwable t) {

            }
        });
    }


    @Override
    public int getItemCount() { return dataList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView no_detail_surat,judul_detail_surat,harga_poin,harga_coret_poin,text_status,text_aksi;
        LinearLayout rekam,layoutStatus,data;
        ImageView ic_status,ic_aksi;

        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            no_detail_surat = itemView.findViewById(R.id.no_detail_surat);
            judul_detail_surat = itemView.findViewById(R.id.judul_detail_surat);
            harga_poin = itemView.findViewById(R.id.harga_poin);
            rekam= itemView.findViewById(R.id.rekam);
            text_status=itemView.findViewById(R.id.text_status);
            text_aksi = itemView.findViewById(R.id.text_aksi);
            ic_aksi = itemView.findViewById(R.id.ic_aksi);
            ic_status = itemView.findViewById(R.id.ic_status);
            layoutStatus = itemView.findViewById(R.id.layoutStatus);
            harga_coret_poin = itemView.findViewById(R.id.harga_coret_poin);
            data = itemView.findViewById(R.id.data);

        }
    }
}
