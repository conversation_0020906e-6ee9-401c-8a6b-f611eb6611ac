package co.metode.hamim.allMenuBeranda.murojaah;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterJuzCekBacaan;
import co.metode.hamim.allMenuBeranda.CekBacaan.CekBacaan;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.juz.JuzItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Murojaah extends AppCompatActivity {
    TextView text_menu;
    ConstraintLayout back;
    AdapterJuzMurojaah adapterJuzMurojaah;
    RecyclerView RcJuzCekBacaan;
    LinearLayoutManager linearLayoutManager;
    LinearLayout layout;
    List<JuzItem> posts;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_murojaah);
        setStatusBarTransparent();

//        Inisialisasi
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        RcJuzCekBacaan = findViewById(R.id.RcJuzCekBacaan);
        linearLayoutManager = new LinearLayoutManager(this);
        RcJuzCekBacaan.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerJuz);

//        Set
        text_menu.setText("Hafalan Yuk!");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

//        get
        list_juz(1);
    }

    private void list_juz(int parameter) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<JuzItem>> call = apiInterface.getJuzMurojaah();
        call.enqueue(new Callback<List<JuzItem>>() {
            @Override
            public void onResponse(Call<List<JuzItem>> call, Response<List<JuzItem>> response) {
                if (response.isSuccessful()){
                    posts = response.body();
                    adapterJuzMurojaah = new AdapterJuzMurojaah(Murojaah.this, posts);
                    RcJuzCekBacaan.setAdapter(adapterJuzMurojaah);

                    if (parameter == 1){
                        RcJuzCekBacaan.setTranslationY(RcJuzCekBacaan.getHeight());
                        layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                        layout.setTranslationZ(0);
                        RcJuzCekBacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    }
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<JuzItem>> call, Throwable t) {
                Log.e("NetworkError", "Periksa Internet Anda");
            }
        });
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (posts != null){
            list_juz(0);
        }
    }
}