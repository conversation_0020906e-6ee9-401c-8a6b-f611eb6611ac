package co.metode.hamim.allMenuBeranda.murojaah;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.ArrayList;
import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterSuratCekBacaan;
import co.metode.hamim.allMenuBeranda.murojaah.datamurojaah.DataPilihanMurojaahItem;
import co.metode.hamim.api.ApiInterface;

public class AdapterPlayMurojaah extends RecyclerView.Adapter<AdapterPlayMurojaah.AdapterHolder> {
    private Context context;
    private List<DataPilihanMurojaahItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;
    ArrayList<String> arrayListNamaSurat;
    ArrayList<String> arrayListUrl;

    public AdapterPlayMurojaah(Context context, List<DataPilihanMurojaahItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }
    @NonNull
    @Override
    public AdapterPlayMurojaah.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_pilihan_gabungan_surat,parent,false);
        AdapterPlayMurojaah.AdapterHolder holder = new AdapterPlayMurojaah.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterPlayMurojaah.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        int nomor = position + 1;


        final DataPilihanMurojaahItem daftarsuratItem = dataList.get(position);
        String id_murojaah = daftarsuratItem.getIdMurojaah();
        String judul = daftarsuratItem.getNamaMurojaah();
        String id_juz = daftarsuratItem.getIdJuz();
        String id_surat = daftarsuratItem.getIdSurat();
        String url_audio = daftarsuratItem.getAudioSurat();
        String nama_surat = ((Activity)context).getIntent().getStringExtra("nama_surat");
        String halaman = ((Activity)context).getIntent().getStringExtra("halaman");
        String img = ((Activity)context).getIntent().getStringExtra("img");
        String arti_surat = ((Activity)context).getIntent().getStringExtra("arti_surat");
        String jenis_murojaah = ((Activity)context).getIntent().getStringExtra("jenis_murojaah");

        arrayListNamaSurat = new ArrayList<>();
        arrayListUrl = new ArrayList<>();
        for (int i=0; i<dataList.size();i++){
            arrayListNamaSurat.add(dataList.get(i).getNamaMurojaah());
            arrayListUrl.add(dataList.get(i).getAudioSurat());
        }

        holder.no_detail_surat.setText(String.valueOf(nomor));
        holder.judul_detail_surat.setText(judul);
        if (jenis_murojaah.equals("2")){
            holder.putar_audio.setVisibility(View.GONE);
            holder.data.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                Toast.makeText(context, title, Toast.LENGTH_LONG).show();
                    Intent intent = new Intent(context, DetailSambungSurat.class);
                    intent.putExtra("nama_surat", judul);
                    intent.putExtra("id_juz", id_juz);
                    intent.putExtra("id_surat", id_surat);
                    intent.putExtra("jenis_murojaah", jenis_murojaah);
                    context.startActivity(intent);
                }
            });
        }else {
            String posisiMushaf;
            if(jenis_murojaah.equals("1")){
                posisiMushaf = ((Activity)context).getIntent().getStringExtra("posisiMushaf");
            }else {
                posisiMushaf = daftarsuratItem.getPosisi();
            }
            holder.putar_audio.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Intent intent = new Intent(context, PlayMurojaahFinal.class);
                    intent.putExtra("id_juz", id_juz);
                    intent.putExtra("posisiMushaf", posisiMushaf);
                    intent.putExtra("judul", judul);
                    intent.putExtra("nama_surat", nama_surat);
                    intent.putExtra("halaman", halaman);
                    intent.putExtra("id_detail_surat", id_murojaah);
                    intent.putExtra("img",img);
                    intent.putExtra("arti_surat",arti_surat);
                    intent.putExtra("url_audio",url_audio);
                    intent.putExtra("from","murojaah");
                    intent.putExtra("arrayListNamaSurat",arrayListNamaSurat);
                    intent.putExtra("arrayListUrl",arrayListUrl);
                    intent.putExtra("posisi", position);
                    intent.putExtra("jenis_murojaah", jenis_murojaah);
                    context.startActivity(intent);
                }
            });
        }

    }


    @Override
    public int getItemCount() { return dataList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView no_detail_surat,judul_detail_surat;
        LinearLayout putar_audio,data;
        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            data = itemView.findViewById(R.id.data);
            no_detail_surat = itemView.findViewById(R.id.no_detail_surat);
            judul_detail_surat = itemView.findViewById(R.id.judul_detail_surat);
            putar_audio = itemView.findViewById(R.id.putar_audio);

        }
    }
}
