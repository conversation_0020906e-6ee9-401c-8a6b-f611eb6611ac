package co.metode.hamim.allMenuBeranda;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager2.widget.ViewPager2;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.tabs.TabLayout;

import java.text.DecimalFormat;

import co.metode.hamim.Home;
import co.metode.hamim.Juz;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.fragment.fragmentSetoran.SetoranAdapter;
import co.metode.hamim.get_saldo.Saldo;
import co.metode.hamim.onBoardingSetoran.onBoardingSetoran;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Setoran extends AppCompatActivity {
    TabLayout tabLayout;
    ViewPager2 viewPager2;
    SetoranAdapter setoranAdapter;
    SessionManager sessionManager;
    TextView namaUser, Point;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setoran);
        SharedPreferences sharedPreferences = getSharedPreferences("prefs", MODE_PRIVATE);
        Boolean firstStart = sharedPreferences.getBoolean("firstStartBoarding", true);
        if (firstStart) {
            firstRun();
        }
        sessionManager = new SessionManager(Setoran.this);

        tabLayout = findViewById(R.id.tab_setoran);
        viewPager2 = findViewById(R.id.pager1);
        namaUser = findViewById(R.id.namauser_setoran);
        Point = findViewById(R.id.point_setoran);

        //add nama user
        namaUser.setText(sessionManager.getUserDetail().get(SessionManager.NAMA));
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);
        get_point(id_user);

        FragmentManager fm = getSupportFragmentManager();
        setoranAdapter = new SetoranAdapter(fm,getLifecycle());
        viewPager2.setAdapter(setoranAdapter);

        tabLayout.addTab(tabLayout.newTab().setText("Buat Setoran"));
        tabLayout.addTab(tabLayout.newTab().setText("Riwayat Setoran"));

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                viewPager2.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        viewPager2.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                tabLayout.selectTab(tabLayout.getTabAt(position));
            }
        });
    }
    private void get_point(String id_user) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<Saldo> call = apiInterface.saldoRespons(id_user);
        call.enqueue(new Callback<Saldo>() {
            @Override
            public void onResponse(Call<Saldo> call, Response<Saldo> response) {
                if (response.isSuccessful()){
                    int saldo = response.body().getSaldo();
                    DecimalFormat decimalFormat = new DecimalFormat("###,###,###");
                    Point.setText("Point "+decimalFormat.format(saldo));
                }
            }

            @Override
            public void onFailure(Call<Saldo> call, Throwable t) {
                Log.e("API_ERROR", "Request failed: " + t.getLocalizedMessage(), t);            }
        });
    }
    private void firstRun() {
        Intent intent = new Intent(Setoran.this, onBoardingSetoran.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NO_HISTORY);
        startActivity(intent);

        SharedPreferences sharedPreferences = getSharedPreferences("prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();

        editor.putBoolean("firstStartBoarding", false);
        editor.apply();
        finish();
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent(Setoran.this, Home.class);
        startActivity(intent);
        finish();
    }
}