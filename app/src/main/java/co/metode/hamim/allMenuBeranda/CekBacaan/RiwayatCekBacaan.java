package co.metode.hamim.allMenuBeranda.CekBacaan;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.CekBacaan.dataulasan.DataUlasanItem;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class RiwayatCekBacaan extends AppCompatActivity {
    ImageView back;
    RecyclerView RcRiwayatCekBacaan;
    LinearLayoutManager linearLayoutManager;
    SessionManager sessionManager;
    String id_user;
    LinearLayout shimmerRiwayatBacaan;
    AdapterRiwayatCekBacaan adapterRiwayatCekBacaan;
    List<DataUlasanItem> posts;
    NestedScrollView scroll;
    int page = 0, limit = 20, data=0, pages;
    ArrayList<String> arrayList = new ArrayList<>();
    LinearLayout loadScroll;
    Handler handler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_riwayat_cek_bacaan);
        setStatusBarTransparent();

        RcRiwayatCekBacaan = findViewById(R.id.RcRiwayatCekBacaan);
        shimmerRiwayatBacaan = findViewById(R.id.shimmerRiwayatBacaan);
        scroll = findViewById(R.id.scroll);
        loadScroll = findViewById(R.id.loadScroll);
        back = findViewById(R.id.back);

        sessionManager = new SessionManager(RiwayatCekBacaan.this);
        id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        linearLayoutManager = new LinearLayoutManager(RiwayatCekBacaan.this);
        RcRiwayatCekBacaan.setLayoutManager(linearLayoutManager);

        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        scroll.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (scrollY == v.getChildAt(0).getMeasuredHeight() - v.getMeasuredHeight()){
                    page = page+data;
                    loadScroll.setVisibility(View.VISIBLE);
                    get_riwayat(0, page, limit);
                }
            }
        });


        get_riwayat(1, page,limit);
    }

    private void get_riwayat(int parameter,int page, int limit) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DataUlasanItem>> call = apiInterface.getUlasanBacaanTerakhir(id_user,page,limit);
        call.enqueue(new Callback<List<DataUlasanItem>>() {
            @Override
            public void onResponse(Call<List<DataUlasanItem>> call, Response<List<DataUlasanItem>> response) {
                if (response.isSuccessful()){
                    String id_setoran = response.body().get(0).getIdSetor();
                    if (!id_setoran.equals("")){
                        data = response.body().size();
                        Gson gson = new Gson();
                        for (int i=0;i<response.body().size();i++){
                            String json = gson.toJson(response.body().get(i));
                            arrayList.add(json);
                        }
                        set_data(arrayList, parameter);

                    }else {
                        shimmerRiwayatBacaan.setVisibility(View.GONE);
                        Toast.makeText(RiwayatCekBacaan.this, "Bacaanmu sudah paling terkahir", Toast.LENGTH_SHORT).show();
                    }
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            loadScroll.setVisibility(View.GONE);
                        }
                    }, 200);
                }
            }

            @Override
            public void onFailure(Call<List<DataUlasanItem>> call, Throwable t) {
                loadScroll.setVisibility(View.GONE);
                Log.w("NetworkWarning", "Periksa Internet Anda");
            }
        });
    }

    private void set_data(ArrayList<String> arrayList, int parameter) {
        adapterRiwayatCekBacaan = new AdapterRiwayatCekBacaan(RiwayatCekBacaan.this, arrayList);
        RcRiwayatCekBacaan.setAdapter(adapterRiwayatCekBacaan);


        if (parameter == 1){
            scroll.setTranslationY(scroll.getHeight());
            shimmerRiwayatBacaan.animate().alpha(0.0f).setDuration(500).translationY(shimmerRiwayatBacaan.getHeight());
            shimmerRiwayatBacaan.setTranslationZ(0);
            scroll.animate().alpha(1.0f).translationY(0).setDuration(1000);
        }
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        set_data(arrayList, 0);
    }

}