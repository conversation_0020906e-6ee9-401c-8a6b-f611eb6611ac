package co.metode.hamim.allMenuBeranda.CekBacaan;

import com.google.gson.annotations.SerializedName;

public class DataCekStatus{

	@SerializedName("id_detail_surat")
	private String idDetailSurat;

	@SerializedName("id_setoran")
	private String idSetoran;

	@SerializedName("catatan")
	private String catatan;

	@SerializedName("id_user")
	private String idUser;

	@SerializedName("message")
	private String message;

	@SerializedName("id_surat")
	private String idSurat;

	@SerializedName("audio_user")
	private String audioUser;

	@SerializedName("id_juz")
	private String idJuz;

	@SerializedName("audio_ust")
	private String audioUst;

	@SerializedName("tgl_balas")
	private String tglBalas;

	@SerializedName("tgl_kirim")
	private String tglKirim;

	@SerializedName("nama_ust")
	private String namaUst;

	@SerializedName("status")
	private String status;

	public void setIdDetailSurat(String idDetailSurat){
		this.idDetailSurat = idDetailSurat;
	}

	public String getIdDetailSurat(){
		return idDetailSurat;
	}

	public void setIdSetoran(String idSetoran){
		this.idSetoran = idSetoran;
	}

	public String getIdSetoran(){
		return idSetoran;
	}

	public void setCatatan(String catatan){
		this.catatan = catatan;
	}

	public String getCatatan(){
		return catatan;
	}

	public void setIdUser(String idUser){
		this.idUser = idUser;
	}

	public String getIdUser(){
		return idUser;
	}

	public void setMessage(String message){
		this.message = message;
	}

	public String getMessage(){
		return message;
	}

	public void setIdSurat(String idSurat){
		this.idSurat = idSurat;
	}

	public String getIdSurat(){
		return idSurat;
	}

	public void setAudioUser(String audioUser){
		this.audioUser = audioUser;
	}

	public String getAudioUser(){
		return audioUser;
	}

	public void setIdJuz(String idJuz){
		this.idJuz = idJuz;
	}

	public String getIdJuz(){
		return idJuz;
	}

	public void setAudioUst(String audioUst){
		this.audioUst = audioUst;
	}

	public String getAudioUst(){
		return audioUst;
	}

	public void setTglBalas(String tglBalas){
		this.tglBalas = tglBalas;
	}

	public String getTglBalas(){
		return tglBalas;
	}

	public void setTglKirim(String tglKirim){
		this.tglKirim = tglKirim;
	}

	public String getTglKirim(){
		return tglKirim;
	}

	public void setNamaUst(String namaUst){
		this.namaUst = namaUst;
	}

	public String getNamaUst(){
		return namaUst;
	}

	public void setStatus(String status){
		this.status = status;
	}

	public String getStatus(){
		return status;
	}
}