package co.metode.hamim.allMenuBeranda.murojaah.datamurojaah;

import com.google.gson.annotations.SerializedName;

public class DataPilihanMurojaahItem{

	@SerializedName("audio_surat")
	private String audioSurat;

	@SerializedName("id_juz")
	private String idJuz;

	@SerializedName("id_murojaah")
	private String idMurojaah;

	@SerializedName("id_surat")
	private String idSurat;

	@SerializedName("jenis_murojaah")
	private String jenisMurojaah;

	@SerializedName("nama_murojaah")
	private String namaMurojaah;

	@SerializedName("posisi")
	private String posisi;

	public void setPosisi(String posisi){
		this.posisi = posisi;
	}

	public String getPosisi(){
		return posisi;
	}

	public void setAudioSurat(String audioSurat){
		this.audioSurat = audioSurat;
	}

	public String getAudioSurat(){
		return audioSurat;
	}

	public void setIdJuz(String idJuz){
		this.idJuz = idJuz;
	}

	public String getIdJuz(){
		return idJuz;
	}

	public void setIdMurojaah(String idMurojaah){
		this.idMurojaah = idMurojaah;
	}

	public String getIdMurojaah(){
		return idMurojaah;
	}

	public void setIdSurat(String idSurat){
		this.idSurat = idSurat;
	}

	public String getIdSurat(){
		return idSurat;
	}

	public void setJenisMurojaah(String jenisMurojaah){
		this.jenisMurojaah = jenisMurojaah;
	}

	public String getJenisMurojaah(){
		return jenisMurojaah;
	}

	public void setNamaMurojaah(String namaMurojaah){
		this.namaMurojaah = namaMurojaah;
	}

	public String getNamaMurojaah(){
		return namaMurojaah;
	}
}