package co.metode.hamim.allMenuBeranda.CekBacaan;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.util.List;

import co.metode.hamim.Detail_surat;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.complete_surat.CompleteSurat;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterSuratCekBacaan extends RecyclerView.Adapter<AdapterSuratCekBacaan.AdapterHolder> {

    private Context context;
    private List<DaftarsuratItem> dataList;
    private String id_juz;
    private ApiInterface apiInterface;
    private int diskon_point;

    public AdapterSuratCekBacaan(Context context, List<DaftarsuratItem> dataList, String id_juz, int diskon_point){
            this.context = context;
            this.dataList = dataList;
            this.id_juz = id_juz;
            this.diskon_point = diskon_point;
    }
    @NonNull
    @Override
    public AdapterSuratCekBacaan.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_surat_cek_bacaan,parent,false);
        AdapterHolder holder = new AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterSuratCekBacaan.AdapterHolder holder, int position) {

        final DaftarsuratItem daftarsuratItem = dataList.get(position);
        String id = daftarsuratItem.getId_surat();
        String title = daftarsuratItem.getNama_surat();
        String arti_surat = daftarsuratItem.getArti_surat();
        String body = daftarsuratItem.getText();
        String halaman = daftarsuratItem.getHalaman();
        String img = daftarsuratItem.getGambar();
        String posisi = daftarsuratItem.getPosisi();


        holder.nomorSuratCekBacaan.setText(id+". ");
        holder.namaSuratCekBacaan.setText(title);

        holder.bodySuratCekBacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(context, DaftarSuratCekBacaan.class);
                intent.putExtra("nama_surat", title);
                intent.putExtra("id_surat", id);
                intent.putExtra("id_juz", id_juz);
                intent.putExtra("diskon_poin", String.valueOf(diskon_point));
                context.startActivity(intent);
            }
        });

        if (img.equals("")) {

        }else{
            Picasso.get().load(img)
                    .into(holder.imgSuratCekBacaan);
        }

    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView nomorSuratCekBacaan,namaSuratCekBacaan;
        ImageView imgSuratCekBacaan;
        LinearLayout bodySuratCekBacaan;
        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            nomorSuratCekBacaan = itemView.findViewById(R.id.nomorSuratCekBacaan);
            namaSuratCekBacaan = itemView.findViewById(R.id.namaSuratCekBacaan);
            imgSuratCekBacaan = itemView.findViewById(R.id.imgSuratCekBacaan);
            bodySuratCekBacaan = itemView.findViewById(R.id.bodySuratCekBacaan);

        }
    }

}
