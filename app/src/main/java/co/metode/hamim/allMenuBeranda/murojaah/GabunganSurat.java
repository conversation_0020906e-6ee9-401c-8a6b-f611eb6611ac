package co.metode.hamim.allMenuBeranda.murojaah;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterDaftarSuratCekBacaan;
import co.metode.hamim.allMenuBeranda.murojaah.datamurojaah.DataPilihanMurojaah;
import co.metode.hamim.allMenuBeranda.murojaah.datamurojaah.DataPilihanMurojaahItem;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class GabunganSurat extends AppCompatActivity {
    TextView text_menu,namaSurat;
    ConstraintLayout back;
    RecyclerView rcMurojaah;
    LinearLayoutManager linearLayoutManager;
    String id_juz;
    LinearLayout layout;
    AdapterPilihanGabunganSurat adapterPilihanGabunganSurat;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gabungan_ayat);
        setStatusBarTransparent();

        //inialisasi
        namaSurat = findViewById(R.id.namaSurat);
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        rcMurojaah = findViewById(R.id.rcMurojaah);
        linearLayoutManager = new LinearLayoutManager(GabunganSurat.this);
        rcMurojaah.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerDaftarSurat);
        id_juz = getIntent().getStringExtra("id_juz");

        //Set
        namaSurat.setText("Juz "+id_juz);
        text_menu.setText("Hafalan Yuk!");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        get_pilihan(0);
    }

    private void get_pilihan(int parameter) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DataPilihanMurojaahItem>> call = apiInterface.getPilihanMurojaah("0",id_juz,"0","2");
        call.enqueue(new Callback<List<DataPilihanMurojaahItem>>() {
            @Override
            public void onResponse(Call<List<DataPilihanMurojaahItem>> call, Response<List<DataPilihanMurojaahItem>> response) {
                if (response.isSuccessful()){
                    List<DataPilihanMurojaahItem> posts = response.body();
                    adapterPilihanGabunganSurat = new AdapterPilihanGabunganSurat(GabunganSurat.this, posts);
                    rcMurojaah.setAdapter(adapterPilihanGabunganSurat);

                    if (parameter == 0){
                        rcMurojaah.setTranslationY(rcMurojaah.getHeight());
                        layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                        layout.setTranslationZ(0);
                        rcMurojaah.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    }
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<DataPilihanMurojaahItem>> call, Throwable t) {

            }
        });
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        get_pilihan(1);
    }
}