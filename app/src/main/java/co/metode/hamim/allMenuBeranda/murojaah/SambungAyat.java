package co.metode.hamim.allMenuBeranda.murojaah;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterDaftarSuratCekBacaan;
import co.metode.hamim.allMenuBeranda.CekBacaan.DaftarSuratCekBacaan;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import co.metode.hamim.detail_surat.DataItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SambungAyat extends AppCompatActivity {
    String id_juz;
    TextView namaSurat;
    TextView text_menu;
    ConstraintLayout back;
    RecyclerView RcDaftarSuratCekBacaan;
    LinearLayoutManager linearLayoutManager;
    LinearLayout layout;
    AdapterDaftarSuratMurojaah adapterDaftarSuratMurojaah;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sambung_ayat);
        setStatusBarTransparent();

        // Inisialisasi
        namaSurat = findViewById(R.id.namaSurat);
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        RcDaftarSuratCekBacaan = findViewById(R.id.RcDaftarSuratCekBacaan);
        linearLayoutManager = new LinearLayoutManager(SambungAyat.this);
        RcDaftarSuratCekBacaan.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerDaftarSurat);

        // Get
        id_juz = getIntent().getStringExtra("id_juz");


        //Set
        namaSurat.setText("Juz "+id_juz);
        text_menu.setText("Hafalan Yuk!");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        daftar_surat(0);
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void daftar_surat(int parameter) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DaftarsuratItem>> call = apiInterface.getPos(id_juz);
        call.enqueue(new Callback<List<DaftarsuratItem>>() {
            @Override
            public void onResponse(Call<List<DaftarsuratItem>> call, Response<List<DaftarsuratItem>> response) {
                if (response.isSuccessful()){
                    List<DaftarsuratItem> posts = response.body();
                    adapterDaftarSuratMurojaah = new AdapterDaftarSuratMurojaah(SambungAyat.this, posts);
                    RcDaftarSuratCekBacaan.setAdapter(adapterDaftarSuratMurojaah);

                    if (parameter == 0){
                        RcDaftarSuratCekBacaan.setTranslationY(RcDaftarSuratCekBacaan.getHeight());
                        layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                        layout.setTranslationZ(0);
                        RcDaftarSuratCekBacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    }
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<DaftarsuratItem>> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Periksa Internet Anda", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        daftar_surat(1);
    }
}