package co.metode.hamim.allMenuBeranda.murojaah;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.squareup.picasso.Picasso;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterSuratCekBacaan;
import co.metode.hamim.allMenuBeranda.murojaah.datamurojaah.DataPilihanMurojaahItem;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;

public class AdapterPilihanGabunganSurat extends RecyclerView.Adapter<AdapterPilihanGabunganSurat.AdapterHolder> {
    private Context context;
    private List<DataPilihanMurojaahItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterPilihanGabunganSurat(Context context, List<DataPilihanMurojaahItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }
    @NonNull
    @Override
    public AdapterPilihanGabunganSurat.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_pilihan_gabungan_surat,parent,false);
        AdapterPilihanGabunganSurat.AdapterHolder holder = new AdapterPilihanGabunganSurat.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterPilihanGabunganSurat.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        int nomor = position + 1;

        final DataPilihanMurojaahItem daftarsuratItem = dataList.get(position);
        String id_murojaah = daftarsuratItem.getIdMurojaah();
        String judul = daftarsuratItem.getNamaMurojaah();
        String id_juz = ((Activity)context).getIntent().getStringExtra("id_juz");
        String img = ((Activity)context).getIntent().getStringExtra("img");
        String id_surat = daftarsuratItem.getIdSurat();
        String posisi = daftarsuratItem.getPosisi();
        String jenis_murojaah = daftarsuratItem.getJenisMurojaah();

        holder.no_detail_surat.setText(String.valueOf(nomor));
        holder.judul_detail_surat.setText(judul);
        holder.putar_audio.setVisibility(View.GONE);

        holder.data.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Toast.makeText(context, title, Toast.LENGTH_LONG).show();
                Intent intent = new Intent(context, DetailSambungSurat.class);
                intent.putExtra("nama_surat", judul);
                intent.putExtra("id_murojaah", id_murojaah);
                intent.putExtra("id_juz", id_juz);
                intent.putExtra("id_surat", id_surat);
                intent.putExtra("jenis_murojaah", "3");
                intent.putExtra("posisiMushaf",posisi);
                intent.putExtra("halaman","0");
                intent.putExtra("img",img);
                intent.putExtra("arti_surat",id_juz);
                context.startActivity(intent);
            }
        });

    }


    @Override
    public int getItemCount() { return dataList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView no_detail_surat,judul_detail_surat;
        LinearLayout putar_audio,data;
        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            data = itemView.findViewById(R.id.data);
            no_detail_surat = itemView.findViewById(R.id.no_detail_surat);
            judul_detail_surat = itemView.findViewById(R.id.judul_detail_surat);
            putar_audio = itemView.findViewById(R.id.putar_audio);

        }
    }
}
