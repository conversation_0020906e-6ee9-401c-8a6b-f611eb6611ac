package co.metode.hamim.allMenuBeranda.CekBacaan;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.detail_surat.DataItem;
import co.metode.hamim.juz.JuzItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class DaftarSuratCekBacaan extends AppCompatActivity {
    String nama_surat, id_surat;
    TextView namaSurat;
    TextView text_menu;
    ImageView back;
    RecyclerView RcDaftarSuratCekBacaan;
    LinearLayoutManager linearLayoutManager;
    LinearLayout layout;
    AdapterDaftarSuratCekBacaan adapterDaftarSuratCekBacaan;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_daftar_surat_cek_bacaan);
        setStatusBarTransparent();

        // Inisialisasi
        namaSurat = findViewById(R.id.namaSurat);
        text_menu = findViewById(R.id.toolbar).findViewById(R.id.toolbar_text);
        back = findViewById(R.id.back);
        RcDaftarSuratCekBacaan = findViewById(R.id.RcDaftarSuratCekBacaan);
        linearLayoutManager = new LinearLayoutManager(DaftarSuratCekBacaan.this);
        RcDaftarSuratCekBacaan.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerDaftarSurat);



        // Get
        nama_surat = getIntent().getStringExtra("nama_surat");
        id_surat = getIntent().getStringExtra("id_surat");


        //Set
        namaSurat.setText(nama_surat);
        text_menu.setText("");
        back.setColorFilter(getResources().getColor(R.color.color_V2_3));
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        daftar_surat(0);
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void daftar_surat(int parameter) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DataItem>> call = apiInterface.getPotongan(id_surat);
        call.enqueue(new Callback<List<DataItem>>() {
            @Override
            public void onResponse(Call<List<DataItem>> call, Response<List<DataItem>> response) {
                if (response.isSuccessful()){
                    List<DataItem> posts = response.body();
                    adapterDaftarSuratCekBacaan = new AdapterDaftarSuratCekBacaan(DaftarSuratCekBacaan.this, posts);
                    RcDaftarSuratCekBacaan.setAdapter(adapterDaftarSuratCekBacaan);

                    if (parameter == 0){
                        RcDaftarSuratCekBacaan.setTranslationY(RcDaftarSuratCekBacaan.getHeight());
                        layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                        layout.setTranslationZ(0);
                        RcDaftarSuratCekBacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    }
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<DataItem>> call, Throwable t) {
                Toast.makeText(getApplicationContext(), "Periksa Internet Anda", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        daftar_surat(1);
    }
}