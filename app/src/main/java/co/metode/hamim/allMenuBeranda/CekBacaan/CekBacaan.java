package co.metode.hamim.allMenuBeranda.CekBacaan;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import co.metode.hamim.AdapterJuz;
import co.metode.hamim.R;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.juz.JuzItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CekBacaan extends AppCompatActivity {
    TextView text_menu;
    ConstraintLayout back;
    AdapterJuzCekBacaan adapterJuzCekBacaan;
    RecyclerView RcJuzCekBacaan;
    LinearLayoutManager linearLayoutManager;
    LinearLayout layout,btnGoToRiwayatCekBacaan;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_cek_bacaan);
        setStatusBarTransparent();

//        Inisialisasi
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        RcJuzCekBacaan = findViewById(R.id.RcJuzCekBacaan);
        btnGoToRiwayatCekBacaan = findViewById(R.id.btnGoToRiwayatCekBacaan);
        linearLayoutManager = new LinearLayoutManager(this);
        RcJuzCekBacaan.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerJuz);

//        Set
        text_menu.setText("");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        btnGoToRiwayatCekBacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(CekBacaan.this, RiwayatCekBacaan.class);
                startActivity(intent);
            }
        });

//        get
        list_juz();

    }

    private void list_juz() {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<JuzItem>> call = apiInterface.getJuzCekBacaan();
        call.enqueue(new Callback<List<JuzItem>>() {
            @Override
            public void onResponse(Call<List<JuzItem>> call, Response<List<JuzItem>> response) {
                if (response.isSuccessful()){
                    List<JuzItem> posts = response.body();
                    adapterJuzCekBacaan = new AdapterJuzCekBacaan(CekBacaan.this, posts);
                    RcJuzCekBacaan.setAdapter(adapterJuzCekBacaan);

                    RcJuzCekBacaan.setTranslationY(RcJuzCekBacaan.getHeight());
                    layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                    layout.setTranslationZ(0);
                    RcJuzCekBacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<JuzItem>> call, Throwable t) {
                Log.e("NetworkError", "Periksa Internet Anda");
            }
        });
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }
}