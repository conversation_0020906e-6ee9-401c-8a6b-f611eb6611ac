package co.metode.hamim.allMenuBeranda.murojaah;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.allMenuBeranda.murojaah.datamurojaah.DataPilihanMurojaahItem;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class DetailSambungSurat extends AppCompatActivity {
    TextView namaSurat;
    TextView text_menu;
    ConstraintLayout back;
    String nama_surat,id_juz,id_murojaah,id_surat,jenis_murojaah,posisiMushaf;
    RecyclerView rcPilihanPlayMurojaah;
    LinearLayoutManager linearLayoutManager;
    LinearLayout layout;
    AdapterPlayMurojaah adapterPlayMurojaah;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_detail_sambung_surat);
        setStatusBarTransparent();

        //inisialisasi
        namaSurat = findViewById(R.id.namaSurat);
        nama_surat = getIntent().getStringExtra("nama_surat");
        id_murojaah = getIntent().getStringExtra("id_murojaah");
        id_juz = getIntent().getStringExtra("id_juz");
        id_surat = getIntent().getStringExtra("id_surat");
        jenis_murojaah = getIntent().getStringExtra("jenis_murojaah");
        posisiMushaf = getIntent().getStringExtra("posisiMushaf");
        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        rcPilihanPlayMurojaah = findViewById(R.id.rcPilihanPlayMurojaah);
        linearLayoutManager = new LinearLayoutManager(DetailSambungSurat.this);
        rcPilihanPlayMurojaah.setLayoutManager(linearLayoutManager);
        layout = findViewById(R.id.shimmerDaftarSurat);


        //set
        namaSurat.setText(nama_surat);
        text_menu.setText("Hafalan Yuk!");
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        get_play(0);

    }

    private void get_play(int parameter) {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DataPilihanMurojaahItem>> call = apiInterface.getPilihanMurojaah(id_murojaah,id_juz,id_surat,jenis_murojaah);
        call.enqueue(new Callback<List<DataPilihanMurojaahItem>>() {
            @Override
            public void onResponse(Call<List<DataPilihanMurojaahItem>> call, Response<List<DataPilihanMurojaahItem>> response) {
                if (response.isSuccessful()){
                    List<DataPilihanMurojaahItem> posts = response.body();
                    adapterPlayMurojaah = new AdapterPlayMurojaah(DetailSambungSurat.this, posts);
                    rcPilihanPlayMurojaah.setAdapter(adapterPlayMurojaah);

                    if (parameter == 0){
                        rcPilihanPlayMurojaah.setTranslationY(rcPilihanPlayMurojaah.getHeight());
                        layout.animate().alpha(0.0f).setDuration(500).translationY(layout.getHeight());
                        layout.setTranslationZ(0);
                        rcPilihanPlayMurojaah.animate().alpha(1.0f).translationY(0).setDuration(1000);
                    }
                    return;
                }
            }

            @Override
            public void onFailure(Call<List<DataPilihanMurojaahItem>> call, Throwable t) {

            }
        });
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        get_play(1);
    }
}