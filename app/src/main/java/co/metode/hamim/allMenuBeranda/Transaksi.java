package co.metode.hamim.allMenuBeranda;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager2.widget.ViewPager2;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.material.tabs.TabLayout;

import co.metode.hamim.Home;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.fragment.fragmentSetoran.SetoranAdapter;
import co.metode.hamim.fragment.fragmentTransaksi.TransaksiAdapter;

public class Transaksi extends AppCompatActivity {
    TabLayout tabLayout;
    ViewPager2 pager_transaksi;
    TransaksiAdapter transaksiAdapter;
    SessionManager sessionManager;
    ConstraintLayout back;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_transaksi);
        setStatusBarTransparent();

        sessionManager = new SessionManager(Transaksi.this);

        tabLayout = findViewById(R.id.tab_transaksi);
        pager_transaksi = findViewById(R.id.pager_transaksi);
        back = findViewById(R.id.back);

        back.setOnClickListener(v -> onBackPressed());

        FragmentManager fm = getSupportFragmentManager();
        transaksiAdapter = new TransaksiAdapter(fm,getLifecycle());
        pager_transaksi.setAdapter(transaksiAdapter);

        tabLayout.addTab(tabLayout.newTab().setText("Belum Bayar"));
        tabLayout.addTab(tabLayout.newTab().setText("Selesai"));
        tabLayout.addTab(tabLayout.newTab().setText("Batal"));

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                pager_transaksi.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        pager_transaksi.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                tabLayout.selectTab(tabLayout.getTabAt(position));
            }
        });
    }

    @Override
    public void onBackPressed() {
        String asal = getIntent().getStringExtra("asal");
        if (asal != null && asal.equals("complete_transaksi")) {
            // Kembali ke Home jika berasal dari CompleteTransaksi
            Intent intent = new Intent(this, Home.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        } else if (asal != null && asal.equals("profile")) {
            finish();
        } else {
            super.onBackPressed();
        }
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }
}