package co.metode.hamim.allMenuBeranda.CekBacaan;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;

import android.content.Intent;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class DetailCekBacaan extends AppCompatActivity {
    ImageView play_myrecord,play_ustadrecord,ic_ust,ic_status;
    MediaPlayer mediaPlayer,mediaPlayer2;
    Handler handler,handler1;
    TextView namaSurat,tglKirim,text_status,text_status_balasan,text_catatan;
    AppCompatButton bacaanLain,ulangiRekaman;
    SessionManager sessionManager;
    ScrollView data_cek_bacaan;
    LinearLayout shimmerCekBacaan;
//    TextView text_menu;
    ImageView back;

    String id_detail_surat,id_surat,id_juz,nama_surat,tgl_kirim,status,audio_user,audio_ust,catatan,poin,parameter,id_setoran;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_detail_cek_bacaan);
        setStatusBarTransparent();


        //inisialisasi
        handler = new Handler();
        handler1 = new Handler();
        parameter = getIntent().getStringExtra("parameter");
        id_detail_surat = getIntent().getStringExtra("id_detail_surat");
        nama_surat = getIntent().getStringExtra("nama_surat");
        id_juz = getIntent().getStringExtra("id_juz");
        id_surat = getIntent().getStringExtra("id_surat");
        poin = getIntent().getStringExtra("poin");

        if (!parameter.equals("1")){
            audio_user = getIntent().getStringExtra("audio_user");
            audio_ust = getIntent().getStringExtra("audio_ust");
            tgl_kirim = getIntent().getStringExtra("tgl_kirim");
            status = getIntent().getStringExtra("status");
            catatan = getIntent().getStringExtra("catatan");
            id_setoran = getIntent().getStringExtra("id_setoran");
            if (!status.equals("1")){
                ubah_status_baca();
            }
        }

        play_myrecord = findViewById(R.id.play_myrecord);
        play_ustadrecord = findViewById(R.id.play_ustadrecord);
        namaSurat = findViewById(R.id.namaSurat);
        tglKirim = findViewById(R.id.tglKirim);
        ic_ust = findViewById(R.id.ic_ust);
        text_catatan = findViewById(R.id.text_catatan);
        bacaanLain = findViewById(R.id.bacaanLain);
        ulangiRekaman = findViewById(R.id.ulangiRekaman);
        ic_status = findViewById(R.id.ic_status);
//        text_status = findViewById(R.id.text_status);
        text_status_balasan = findViewById(R.id.text_status_balasan);
        data_cek_bacaan = findViewById(R.id.data_cek_bacaan);
        shimmerCekBacaan = findViewById(R.id.shimmerCekBacaan);
//        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);
        mediaPlayer = new MediaPlayer();
        mediaPlayer2 = new MediaPlayer();
        sessionManager = new SessionManager(DetailCekBacaan.this);

        if (parameter.equals("1")){
            String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);
            //get data
            ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
            Call<DataCekStatus> call = apiInterface.cekStatus(id_user, id_detail_surat);
            call.enqueue(new Callback<DataCekStatus>() {
                @Override
                public void onResponse(Call<DataCekStatus> call, Response<DataCekStatus> response) {
                    if (response.isSuccessful()){
                        tgl_kirim = response.body().getTglKirim();
                        status = response.body().getStatus();
                        audio_user = response.body().getAudioUser();
                        audio_ust = response.body().getAudioUst();
                        catatan = response.body().getCatatan();
                        id_setoran = response.body().getIdSetoran();

                        if (response.body().getMessage().equals("berhasil")){
                            tampilkan();
                        }
                    }
                }

                @Override
                public void onFailure(Call<DataCekStatus> call, Throwable t) {
                }
            });
        }else {
            data_cek_bacaan.setTranslationY(data_cek_bacaan.getHeight());
            shimmerCekBacaan.animate().alpha(0.0f).setDuration(500).translationY(shimmerCekBacaan.getHeight());
            shimmerCekBacaan.setTranslationZ(0);
            data_cek_bacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
            preparedMediaPlayer();
            if (!status.equals("1")){
                prepareAudioUst();
            }

            String tStatus = "";
            int drawable;
            if (status.equals("1")){
                tStatus = "Menunggu";
                drawable = R.drawable.ic_menunggu;
                ic_ust.setImageResource(R.drawable.ic_mic_abu);
                play_ustadrecord.setImageResource(R.drawable.ic_media_play_abu);
                catatan = "-";
            }else if (status.equals("2")){
                drawable = R.drawable.ic_pemula;
                tStatus = "Pemula";
            }else if (status.equals("3")){
                drawable = R.drawable.ic_terampil;
                tStatus = "Terampil";
            }else {
                drawable = R.drawable.ic_mahir;
                tStatus = "Mahir";
            }

            //set
            namaSurat.setText(nama_surat);
            tglKirim.setText(tgl_kirim);
            ic_status.setImageResource(drawable);
//            text_status.setText(tStatus);
            text_status_balasan.setText(tStatus);
            text_catatan.setText(catatan);
            namaSurat.setText(nama_surat);
//            text_menu.setText("");
            back.setColorFilter(getResources().getColor(R.color.color_V2_3));
            back.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onBackPressed();
                }
            });

            //onclick
            on_click();
        }
    }

    private void ubah_status_baca() {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<DataStatusBaca> call = apiInterface.getStatusBacaCekBacaan(id_setoran);
        call.enqueue(new Callback<DataStatusBaca>() {
            @Override
            public void onResponse(Call<DataStatusBaca> call, Response<DataStatusBaca> response) {
                if (response.isSuccessful()){
                    String message = response.body().getMessage();
                    if (message.equals("berhasil")){
                    }
                }
            }

            @Override
            public void onFailure(Call<DataStatusBaca> call, Throwable t) {

            }
        });
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void tampilkan() {
        preparedMediaPlayer();
        if (!status.equals("1")){
            prepareAudioUst();
        }

        String tStatus = "";
        int drawable;
        if (status.equals("1")){
            tStatus = "Menunggu";
            drawable = R.drawable.ic_menunggu;
            ic_ust.setImageResource(R.drawable.ic_mic_abu);
            play_ustadrecord.setImageResource(R.drawable.ic_media_play_abu);
            catatan = "-";
        }else if (status.equals("2")){
            drawable = R.drawable.ic_pemula;
            tStatus = "Pemula";
            play_ustadrecord.setImageResource(R.drawable.ic_media_play);
        }else if (status.equals("3")){
            drawable = R.drawable.ic_terampil;
            tStatus = "Terampil";
            play_ustadrecord.setImageResource(R.drawable.ic_media_play);
        }else {
            drawable = R.drawable.ic_mahir;
            tStatus = "Mahir";
            play_ustadrecord.setImageResource(R.drawable.ic_media_play);
        }

        //set
        namaSurat.setText(nama_surat);
        tglKirim.setText(tgl_kirim);
        ic_status.setImageResource(drawable);
//        text_status.setText(tStatus);
        text_status_balasan.setText(tStatus);
        text_catatan.setText(catatan);
        namaSurat.setText(nama_surat);
//        text_menu.setText("");
        back.setColorFilter(getResources().getColor(R.color.color_V2_3));
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        //onclick
        on_click();
        data_cek_bacaan.setTranslationY(data_cek_bacaan.getHeight());
        shimmerCekBacaan.animate().alpha(0.0f).setDuration(500).translationY(shimmerCekBacaan.getHeight());
        shimmerCekBacaan.setTranslationZ(0);
        data_cek_bacaan.animate().alpha(1.0f).translationY(0).setDuration(1000);
    }

    private void on_click() {
        play_myrecord.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                play_user();
            }
        });

        if(!status.equals("1")){
            play_ustadrecord.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    play_ust();
                }
            });
        }

        bacaanLain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        ulangiRekaman.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(DetailCekBacaan.this, RekamBacaan.class);
                intent.putExtra("id_detail_surat", id_detail_surat);
                intent.putExtra("id_surat", id_surat);
                intent.putExtra("id_juz", id_juz);
                intent.putExtra("nama_surat", nama_surat);
                intent.putExtra("harga_poin", poin);
                startActivity(intent);
                finish();
            }
        });
    }

    private void prepareAudioUst() {
        try {
            mediaPlayer2.setDataSource(audio_ust);
            mediaPlayer2.prepareAsync();
        }catch (Exception exception){
            exception.printStackTrace();
        }

        mediaPlayer2.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                play_ustadrecord.setImageResource(R.drawable.ic_media_play);
            }
        });
    }

    private void preparedMediaPlayer(){
        try {
            mediaPlayer.setDataSource(audio_user);
            mediaPlayer.prepareAsync();
        }catch (Exception exception){
            exception.printStackTrace();
        }

        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                play_myrecord.setImageResource(R.drawable.ic_media_play);
            }
        });
    }

    private void play_user() {
        mediaPlayer2.stop();
        if (mediaPlayer.isPlaying()){
            mediaPlayer.pause();
            play_myrecord.setImageResource(R.drawable.ic_media_play);
        }else {
            mediaPlayer.start();
            play_myrecord.setImageResource(R.drawable.ic_media_pause);
        }
    }

    private void play_ust() {
        if (mediaPlayer2.isPlaying()){
            mediaPlayer2.pause();
            play_ustadrecord.setImageResource(R.drawable.ic_media_play);
        }else {
            mediaPlayer2.start();
            play_ustadrecord.setImageResource(R.drawable.ic_media_pause);
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        mediaPlayer2.stop();
        mediaPlayer2.reset();
        mediaPlayer.stop();
        mediaPlayer.reset();
        DetailCekBacaan.this.finish();
    }
}