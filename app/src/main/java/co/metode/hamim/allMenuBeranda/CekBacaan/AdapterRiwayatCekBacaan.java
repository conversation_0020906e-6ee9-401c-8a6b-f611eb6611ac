package co.metode.hamim.allMenuBeranda.CekBacaan;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import co.metode.hamim.MainActivity;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.CekBacaan.dataulasan.CountUlasan;
import co.metode.hamim.allMenuBeranda.CekBacaan.dataulasan.DataUlasanItem;
import co.metode.hamim.allMenuBeranda.murojaah.PilihanMurojaah;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.juz.CekDataHamim;
import co.metode.hamim.juz.JuzItem;
import co.metode.hamim.payment.Checkout;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterRiwayatCekBacaan extends RecyclerView.Adapter<AdapterRiwayatCekBacaan.AdapterHolder> {
    private Context context;
    private List<DataUlasanItem> dataList;
    private ArrayList<String> arrayList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterRiwayatCekBacaan(Context context,ArrayList<String> arrayList){
        this.context = context;
        this.arrayList = arrayList;
    }
    @NonNull
    @Override
    public AdapterRiwayatCekBacaan.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_daftar_riwayat_cek_bacaan,parent,false);
        AdapterRiwayatCekBacaan.AdapterHolder holder = new AdapterRiwayatCekBacaan.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterRiwayatCekBacaan.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);


        try {
            JSONObject jsonArray = new JSONObject(arrayList.get(position));
            String nama_surat = jsonArray.getString("nama_surat");
            String ayat_potong = jsonArray.getString("ayat_potongan");
            String surat = nama_surat+" ("+ayat_potong+")";
            String tgl_kirim = jsonArray.getString("tgl_kirim");
            String status_baca = jsonArray.getString("status_baca");
            String status = jsonArray.getString("status");
            String id_detail_surat = jsonArray.getString("id_detail_surat");
            String audio_user = jsonArray.getString("audio_user");
            String audio_ust = jsonArray.getString("audio_ust");
            String catatan = jsonArray.getString("catatan");
            String poin = jsonArray.getString("poin");
            String diskon_poin = jsonArray.getString("diskon_poin");
            String id_setoran = jsonArray.getString("id_setor");
            String id_surat = jsonArray.getString("id_surat");
            String id_juz = jsonArray.getString("id_juz");

            int nomor = position+1;
            //set
            holder.nama_surat.setText(String.valueOf(nomor)+" "+nama_surat+" ("+ayat_potong+")");
            holder.tgl_cek_bacaan.setText(tgl_kirim);

            int drawable;
            String text_status;
            if (status.equals("1")){
                drawable = R.drawable.ic_menunggu;
                text_status = "Menunggu";
                holder.data.setBackgroundResource(R.color.white);
            }else if (status.equals("2")){
                drawable = R.drawable.ic_pemula;
                text_status = "Pemula";
            }else if (status.equals("3")){
                drawable = R.drawable.ic_terampil;
                text_status = "Terampil";
            }else {
                drawable = R.drawable.ic_mahir;
                text_status = "Mahir";
            }

            holder.ic_status.setImageResource(drawable);
            holder.text_status.setText(text_status);

            int kurangi = Integer.parseInt(poin) - Integer.parseInt(diskon_poin);
            int finalPoin = kurangi;

            holder.data.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Intent intent = new Intent(context, DetailCekBacaan.class);
                    intent.putExtra("id_detail_surat", id_detail_surat);
                    intent.putExtra("nama_surat", surat);
                    intent.putExtra("id_surat", id_surat);
                    intent.putExtra("id_juz", id_juz);
                    intent.putExtra("audio_user", audio_user);
                    intent.putExtra("audio_ust", audio_ust);
                    intent.putExtra("tgl_kirim", tgl_kirim);
                    intent.putExtra("status", status);
                    intent.putExtra("catatan",catatan);
                    intent.putExtra("id_setoran", id_setoran);
                    intent.putExtra("poin", String.valueOf(finalPoin));
                    intent.putExtra("parameter", "0");
                    intent.setFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
                    context.startActivity(intent);
                }
            });

            if (!status.equals("1")){
                //cek status baca
                ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                Call<CountUlasan> call = apiInterface.getStatusBacaBacaan(id_setoran);
                call.enqueue(new Callback<CountUlasan>() {
                    @Override
                    public void onResponse(Call<CountUlasan> call, Response<CountUlasan> response) {
                        if (response.isSuccessful()){
                            if (!response.body().getMessage().equals("0")){
                                holder.data.setBackgroundResource(R.color.white);
                            }else {
                                holder.data.setBackgroundResource(R.color.abumuda);
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<CountUlasan> call, Throwable t) {

                    }
                });
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

    }


    @Override
    public int getItemCount() { return arrayList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView nama_surat,tgl_cek_bacaan,text_status;
        ImageView ic_status;
        LinearLayout data;

        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            nama_surat = itemView.findViewById(R.id.nama_surat);
            tgl_cek_bacaan = itemView.findViewById(R.id.tgl_cek_bacaan);
            ic_status = itemView.findViewById(R.id.ic_status);
            text_status = itemView.findViewById(R.id.text_status);
            data = itemView.findViewById(R.id.data);

        }
    }
}
