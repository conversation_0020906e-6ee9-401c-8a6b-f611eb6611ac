package co.metode.hamim.allMenuBeranda.murojaah;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.squareup.picasso.Picasso;

import java.util.List;

import co.metode.hamim.Detail_surat;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.allMenuBeranda.CekBacaan.AdapterSuratCekBacaan;
import co.metode.hamim.allMenuBeranda.CekBacaan.DataCekStatus;
import co.metode.hamim.allMenuBeranda.CekBacaan.DetailCekBacaan;
import co.metode.hamim.allMenuBeranda.CekBacaan.RekamBacaan;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import co.metode.hamim.detail_surat.DataItem;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterDaftarSuratMurojaah extends RecyclerView.Adapter<AdapterDaftarSuratMurojaah.AdapterHolder> {
    private Context context;
    private List<DaftarsuratItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterDaftarSuratMurojaah(Context context, List<DaftarsuratItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }
    @NonNull
    @Override
    public AdapterDaftarSuratMurojaah.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_surat,parent,false);
        AdapterDaftarSuratMurojaah.AdapterHolder holder = new AdapterDaftarSuratMurojaah.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterDaftarSuratMurojaah.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        int nomor = position + 1;

        final DaftarsuratItem daftarsuratItem = dataList.get(position);
        String id = daftarsuratItem.getId_surat();
        String title = daftarsuratItem.getNama_surat();
        String arti_surat = daftarsuratItem.getArti_surat();
        String body = daftarsuratItem.getText();
        String halaman = daftarsuratItem.getHalaman();
        String img = daftarsuratItem.getGambar();
        String posisi = daftarsuratItem.getPosisi();
        String id_juz = ((Activity)context).getIntent().getStringExtra("id_juz");
        String id_murojaah = "0";


        holder.tv_id.setText(id+".");
        holder.tv_title.setText(title);
        holder.tv_body.setText(arti_surat+" ("+body+")");

        holder.data.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Toast.makeText(context, title, Toast.LENGTH_LONG).show();
                Intent intent = new Intent(context, DetailSambungSurat.class);
                intent.putExtra("nama_surat", title);
                intent.putExtra("id_murojaah", id_murojaah);
                intent.putExtra("id_juz", id_juz);
                intent.putExtra("halaman", halaman);
                intent.putExtra("img", img);
                intent.putExtra("arti_surat", arti_surat);
                intent.putExtra("arti_surat", arti_surat);
                intent.putExtra("id_surat", id);
                intent.putExtra("posisiMushaf", posisi);
                intent.putExtra("jenis_murojaah", "1");
                context.startActivity(intent);
            }
        });

        if (img.equals("")) {

        }else{
            Picasso.get().load(img)
                    .into(holder.icon);
        }
    }


    @Override
    public int getItemCount() { return dataList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView tv_title,tv_body,tv_id,arti_surat;
        CardView data;
        ImageView icon;
        ProgressBar progressBar;

        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            tv_id = itemView.findViewById(R.id.id_no);
            tv_title = itemView.findViewById(R.id.tv_title);
            tv_body = itemView.findViewById(R.id.tv_body);
            data = itemView.findViewById(R.id.data);
            progressBar = itemView.findViewById(R.id.progressSurat);
            icon = itemView.findViewById(R.id.iv_icon);

        }
    }
}
