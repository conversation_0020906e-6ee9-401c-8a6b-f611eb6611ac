package co.metode.hamim.allMenuBeranda.murojaah;

import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.StrictMode;
import android.preference.PreferenceManager;
import android.util.Base64;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.squareup.picasso.Picasso;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Type;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import co.metode.hamim.AdapterPlayList;
import co.metode.hamim.MushafAdapter;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.add_history.AddHistory;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.mushaf.DataItem;
import co.metode.hamim.mushaf.MushafData;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class PlayMurojaah extends AppCompatActivity implements View.OnClickListener {
    ImageView play_button,gmbsurat_b,Thafal;
    MediaPlayer mediaPlayer;
    TextView currentduration,totalduration,namaSurat,artiSurat;
    SeekBar bar;
    Switch ulang;
    LinearLayout loop_btn,upToPlaylist,loadingStream,play_buttonFrame;
    MushafAdapter mushafAdapter;
    ViewPager viewPager;
    int pausePosition;
    private Handler handler = new Handler();
    private Handler handler1 = new Handler();
    private Handler handler2 = new Handler();
    ProgressDialog pd;
    SessionManager sessionManager;
    ApiInterface apiInterface;
    BottomSheetDialog bottomSheetDialog1 = null;
    int loopStream = 0;
    int bottom = 0;
    LinearLayout bg_detail_player;
    TextView text_menu;
    ImageView back, loop;
    ArrayList<String> arrayListNamaSurat;
    ArrayList<String> arrayListUrl;
    AdapterPlayList adapterPlayList;
    RecyclerView recyclerView;
    String from,jumlah_ayat,id_juz;
    int currentSong;
    Thread thread;
    Thread thread1;
    ArrayList<String> listMushafSave;
    RelativeLayout download;
    LinearLayout alertDownloadMushaf, progressLayoutDownload;
    Button btnDownload;
    ProgressBar progressDownload;
    TextView persen;
    Spinner speed_audio;
    float speedAudio = 1f;
    int positioSpeed = 100;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_play_murojaah);
        setStatusBarTransparent();


        viewPager = findViewById(R.id.viewMushaf);
        ulang = findViewById(R.id.switch1);
        gmbsurat_b = findViewById(R.id.gmbsurat_big);
        namaSurat = findViewById(R.id.labelNamaSurat);
        artiSurat = findViewById(R.id.artiSurat);
        upToPlaylist = findViewById(R.id.upToPlaylist);
        download = findViewById(R.id.download);
        progressDownload = findViewById(R.id.progressDownload);
        persen = findViewById(R.id.persen);
        alertDownloadMushaf = findViewById(R.id.alertDownloadMushaf);
        progressLayoutDownload = findViewById(R.id.progressLayoutDownload);
        btnDownload = findViewById(R.id.btnDownloadMushaf);
        loop_btn = findViewById(R.id.loop_btn);
        loadingStream = findViewById(R.id.loadingStream);
        play_buttonFrame = findViewById(R.id.play_buttonFrame);
        loop = findViewById(R.id.loop);
        speed_audio = findViewById(R.id.speed_audio);
        bar = findViewById(R.id.bar);
        play_button = findViewById(R.id.play_button);

        text_menu = findViewById(R.id.text_menu);
        back = findViewById(R.id.back);

        String judul = getIntent().getStringExtra("judul");
        text_menu.setText(judul);
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        bg_detail_player = findViewById(R.id.bg_detail_player);



        id_juz = getIntent().getStringExtra("id_juz");
        String halaman = getIntent().getStringExtra("halaman");
        from = getIntent().getStringExtra("from");

        sessionManager = new SessionManager(PlayMurojaah.this);

        mediaPlayer = new MediaPlayer();

        upToPlaylist.setOnClickListener(this);
        loop_btn.setOnClickListener(this);

//        Set player
        setDataActivity();
        getIncomingExtra();
        preparedMediaPlayer();
        getMushaf(halaman);
    }



    private void getMushaf(String halaman) {
        handler2.postDelayed(new Runnable() {
            @Override
            public void run() {
                listMushafSave = getArrayList("mushafv2"+id_juz);
                if (listMushafSave != null){
                    setData();
                }else{
                    download.setVisibility(View.VISIBLE);
                    btnDownload.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            alertDownloadMushaf.setVisibility(View.GONE);
                            progressLayoutDownload.setVisibility(View.VISIBLE);
                            getData();
                        }
                    });
                }
            }
        },500);
    }

    private void getData() {
        apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<MushafData> call = apiInterface.getMushaf(id_juz);
        call.enqueue(new Callback<MushafData>() {
            @Override
            public void onResponse(Call<MushafData> call, Response<MushafData> response) {
                if (response.isSuccessful()){
                    List<DataItem> list = response.body().getData();
                    if(list.isEmpty()){
                        download.setVisibility(View.GONE);
                        Toast.makeText(PlayMurojaah.this, "Mushaf tidak ditemukan", Toast.LENGTH_SHORT).show();
                    }else {
                        Toast.makeText(PlayMurojaah.this, "Memulai Unduh", Toast.LENGTH_SHORT).show();
                        listMushafSave = new ArrayList<>();
                        thread1 = new Thread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    for (int i=0; i<list.size();i++){
                                        listMushafSave.add(convertUrlToBase64(list.get(i).getUrl()));
                                        int size = list.size() - 1;
                                        int kali = i*100;
                                        int progress = kali/size;
                                        handler1.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                persen.setText(progress+"%");
                                                progressDownload.setProgress(progress);
                                                if (progress == 100){
                                                    handler1.removeCallbacks(thread1);
                                                    saveArrayList(listMushafSave,"mushafv2"+id_juz);
                                                    download.animate().alpha(0.0f).setDuration(500);
                                                    download.setVisibility(View.GONE);
                                                    setData();
                                                    Toast.makeText(PlayMurojaah.this, "Unduh berhasil", Toast.LENGTH_SHORT).show();
                                                }
                                            }
                                        });
                                    }
                                }catch (Exception exception){

                                }
                            }
                        });
                        thread1.start();
                    }
                }
            }

            @Override
            public void onFailure(Call<MushafData> call, Throwable t) {

            }
        });
    }

    public String convertUrlToBase64(String url) {
        URL newurl;
        Bitmap bitmap;
        String base64 = "";
        try {
            StrictMode.ThreadPolicy policy = new StrictMode.ThreadPolicy.Builder().permitAll().build();
            StrictMode.setThreadPolicy(policy);
            newurl = new URL(url);
            bitmap = BitmapFactory.decodeStream(newurl.openConnection().getInputStream());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
            base64 = Base64.encodeToString(outputStream.toByteArray(), Base64.DEFAULT);
//            Log.i("sukses", base64);
        } catch (Exception e) {
            Log.d("gagal", "Gagal Lagi");
            e.printStackTrace();
        }
        return base64;
    }

    public void saveArrayList(ArrayList<String> listMushafSave, String key){
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        SharedPreferences.Editor editor = prefs.edit();
        Gson gson = new Gson();
        String json = gson.toJson(listMushafSave);
        editor.putString(key, json);
        editor.apply();
    }


    private void setData() {
        mushafAdapter = new MushafAdapter(PlayMurojaah.this, null, listMushafSave,from);
        viewPager.setAdapter(mushafAdapter);

        String posisiMushaf = getIntent().getStringExtra("posisiMushaf");
        String[] posisi = posisiMushaf.split(" ");
        int pos = Integer.parseInt(posisi[0]);
        viewPager.setCurrentItem(pos);
    }

    public ArrayList<String> getArrayList(String key){
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        Gson gson = new Gson();
        String json = prefs.getString(key, null);
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        return gson.fromJson(json, type);
    }

    private void setStatusBarTransparent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);

            View decorView = window.getDecorView();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            }
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void getIncomingExtra() {
        if (getIntent().hasExtra("id_surat") && getIntent().hasExtra("jumlah_ayat") && getIntent().hasExtra("nama_surat") && getIntent().hasExtra("img")){

            String id_detail_surat = getIntent().getStringExtra("id_detail_surat");

            String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);
            Integer content = 1;
            //add_history
            if(id_user.equals("")){
                id_user = "0";
            }
            addHistory(id_detail_surat,id_user,content);
        }
    }

    private void addHistory(String id_detail_surat, String id_user, Integer content) {
        apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<AddHistory> call = apiInterface.AddHistory(id_detail_surat, id_user, content);
        call.enqueue(new Callback<AddHistory>() {
            @Override
            public void onResponse(Call<AddHistory> call, Response<AddHistory> response) {

            }

            @Override
            public void onFailure(Call<AddHistory> call, Throwable t) {

            }
        });
    }

    private void setDataActivity() {

        String img = getIntent().getStringExtra("img");
        String nama_surat = getIntent().getStringExtra("nama_surat");
        String arti_surat = getIntent().getStringExtra("arti_surat");
        String jenis_murojaah = getIntent().getStringExtra("jenis_murojaah");

        if(!img.equals("")) {
            Picasso.get().load(img)
                    .into(gmbsurat_b);
        }

        if(jenis_murojaah.equals("3")){
            namaSurat.setText("Juz "+id_juz);
            artiSurat.setText(nama_surat);
        }else{
            namaSurat.setText("Surah "+nama_surat);
            artiSurat.setText(arti_surat);
        }


        //set speed

        String[] items = {"0.25x","0.5x","0.75x","1x","1.25x","1.5x","1.75x","2x"};
        ArrayAdapter<String> adapterItems;

        adapterItems = new ArrayAdapter<>(this,R.layout.list_jenis_setoran,items);
        adapterItems.setDropDownViewResource(R.layout.support_simple_spinner_dropdown_item);
        speed_audio.setAdapter(adapterItems);

        speed_audio.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String kata = parent.getItemAtPosition(position).toString();
                int countText = kata.length();
                int kurangi = countText-1;
                String getSpeed = kata.substring(0,kurangi);
                speedAudio = Float.parseFloat(getSpeed+"f");
                positioSpeed = position;
                if (mediaPlayer.isPlaying()){
                    ubah_speed();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        if (speedAudio == 1f){
            speed_audio.setSelection(3);
        }else {
            speed_audio.setSelection(positioSpeed);
        }
    }

    @SuppressLint("ResourceAsColor")
    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case  R.id.play_button:
                play();
                break;
            case R.id.loop_btn:
                if(loopStream == 1) {
                    loopStream = 0;
                    loop.setColorFilter(ContextCompat.getColor(PlayMurojaah.this, R.color.white), android.graphics.PorterDuff.Mode.MULTIPLY);
                }else {
                    loopStream = 1;
                    loop.setColorFilter(ContextCompat.getColor(PlayMurojaah.this, R.color.orangetua), android.graphics.PorterDuff.Mode.MULTIPLY);
                }
                break;
        }
    }

    private void ubah_speed() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mediaPlayer.setPlaybackParams(mediaPlayer.getPlaybackParams().setSpeed(speedAudio));
        }
    }

    private void filter(int currentSong) {
        adapterPlayList.filterList(currentSong);
    }


    private void preparedMediaPlayer(){

        updateSeekBar();


        bar.setMax(100);

        try {
            String urlAudio = getIntent().getStringExtra( "url_audio");
            mediaPlayer.setDataSource(urlAudio);
            mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            mediaPlayer.prepareAsync();
        }catch (Exception exception){

        }

        mediaPlayer.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
            @Override
            public void onBufferingUpdate(MediaPlayer mediaPlayer, int i) {
                Toast.makeText(PlayMurojaah.this, String.valueOf(i), Toast.LENGTH_SHORT).show();
            }
        });

        mediaPlayer.setOnInfoListener(new MediaPlayer.OnInfoListener() {
            @Override
            public boolean onInfo(MediaPlayer mp, int what, int extra) {
                switch (what) {
                    case MediaPlayer.MEDIA_INFO_BUFFERING_START:
                        loadingStream.setVisibility(View.VISIBLE);
                        play_buttonFrame.setVisibility(View.GONE);
                        break;
                    case MediaPlayer.MEDIA_INFO_BUFFERING_END:
                        loadingStream.setVisibility(View.GONE);
                        play_buttonFrame.setVisibility(View.VISIBLE);
                        break;
                }
                return false;
            }
        });



        mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                mp.start();
                updateSeekBar();
            }
        });



        play_button.setOnClickListener(this);


        bar.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                SeekBar seekBar = (SeekBar) v;
                int playPosition = (mediaPlayer.getDuration() / 100) * seekBar.getProgress();
                mediaPlayer.seekTo(playPosition);
                return false;
            }
        });



        mediaPlayer.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
            @Override
            public void onBufferingUpdate(MediaPlayer mediaPlayer, int i) {
                bar.setSecondaryProgress(i);
            }
        });
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                bar.setProgress(0);
                play_button.setImageResource(R.drawable.play_button);
                if (loopStream == 1){
                    play();
                }
            }
        });
        
    }

    private Runnable updater = new Runnable() {
        @Override
        public void run() {
            updateSeekBar();
        }
    };

    private void updateSeekBar(){
        if (mediaPlayer.isPlaying()){
            play_button.setImageResource(R.drawable.pause_button);
        }else {
            play_button.setImageResource(R.drawable.play_button);
        }
        bar.setProgress((int) (((float) mediaPlayer.getCurrentPosition() / mediaPlayer.getDuration()) * 100));
        handler.postDelayed(updater, 1000);
    }

    private String milliSecondsToTimer(long milliSeconds){
        String timerString = "";
        String secondsString;

        int hours = (int) (milliSeconds / (1000 * 60 * 60));
        int minutes = (int) (milliSeconds % (1000 * 60 * 60)) / (1000 * 60);
        int seconds = (int) (milliSeconds % (1000 * 60 * 60)) % (1000 * 60) / 1000;

        if(hours > 0){
            timerString = hours + ":";
        }
        if (seconds < 10){
            secondsString = "0" + seconds;
        }else {
            secondsString = "" + seconds;
        }

        timerString = timerString + minutes + ":" + secondsString;
        return timerString;

    }


    private void play() {
        if (mediaPlayer.isPlaying()){
            handler.removeCallbacks(updater);
            mediaPlayer.pause();
            play_button.setImageResource(R.drawable.play_button);
        }else {
            mediaPlayer.start();
            play_button.setImageResource(R.drawable.pause_button);
            updateSeekBar();
        }
    }


    @Override
    public void onBackPressed(){
        super.onBackPressed();
        mediaPlayer.reset();
        PlayMurojaah.this.finish();
        handler.removeCallbacks(updater);
    }
}