package co.metode.hamim.allMenuBeranda.CekBacaan.dataulasan;

import com.google.gson.annotations.SerializedName;

public class DataUlasanItem{

	@SerializedName("id_detail_surat")
	private String idDetailSurat;

	@SerializedName("catatan")
	private String catatan;

	@SerializedName("id_setor")
	private String idSetor;

	@SerializedName("nama_surat")
	private String namaSurat;

	@SerializedName("id_surat")
	private String idSurat;

	@SerializedName("audio_user")
	private String audioUser;

	@SerializedName("diskon_poin")
	private String diskonPoin;

	@SerializedName("poin")
	private String poin;

	@SerializedName("id_juz")
	private String idJuz;

	@SerializedName("audio_ust")
	private String audioUst;

	@SerializedName("status_baca")
	private String statusBaca;

	@SerializedName("tgl_kirim")
	private String tglKirim;

	@SerializedName("tgl_balas")
	private String tglBalas;

	@SerializedName("ayat_potongan")
	private String ayatPotongan;

	@SerializedName("status")
	private String status;

	public void setIdDetailSurat(String idDetailSurat){
		this.idDetailSurat = idDetailSurat;
	}

	public String getIdDetailSurat(){
		return idDetailSurat;
	}

	public void setCatatan(String catatan){
		this.catatan = catatan;
	}

	public String getCatatan(){
		return catatan;
	}

	public void setIdSetor(String idSetor){
		this.idSetor = idSetor;
	}

	public String getIdSetor(){
		return idSetor;
	}

	public void setNamaSurat(String namaSurat){
		this.namaSurat = namaSurat;
	}

	public String getNamaSurat(){
		return namaSurat;
	}

	public void setIdSurat(String idSurat){
		this.idSurat = idSurat;
	}

	public String getIdSurat(){
		return idSurat;
	}

	public void setAudioUser(String audioUser){
		this.audioUser = audioUser;
	}

	public String getAudioUser(){
		return audioUser;
	}

	public void setDiskonPoin(String diskonPoin){
		this.diskonPoin = diskonPoin;
	}

	public String getDiskonPoin(){
		return diskonPoin;
	}

	public void setPoin(String poin){
		this.poin = poin;
	}

	public String getPoin(){
		return poin;
	}

	public void setIdJuz(String idJuz){
		this.idJuz = idJuz;
	}

	public String getIdJuz(){
		return idJuz;
	}

	public void setAudioUst(String audioUst){
		this.audioUst = audioUst;
	}

	public String getAudioUst(){
		return audioUst;
	}

	public void setStatusBaca(String statusBaca){
		this.statusBaca = statusBaca;
	}

	public String getStatusBaca(){
		return statusBaca;
	}

	public void setTglKirim(String tglKirim){
		this.tglKirim = tglKirim;
	}

	public String getTglKirim(){
		return tglKirim;
	}

	public void setTglBalas(String tglBalas){
		this.tglBalas = tglBalas;
	}

	public String getTglBalas(){
		return tglBalas;
	}

	public void setAyatPotongan(String ayatPotongan){
		this.ayatPotongan = ayatPotongan;
	}

	public String getAyatPotongan(){
		return ayatPotongan;
	}

	public void setStatus(String status){
		this.status = status;
	}

	public String getStatus(){
		return status;
	}
}