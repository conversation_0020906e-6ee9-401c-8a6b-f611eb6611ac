package co.metode.hamim.allMenuBeranda.CekBacaan;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.squareup.picasso.Picasso;

import java.util.List;

import co.metode.hamim.Audio;
import co.metode.hamim.MainActivity;
import co.metode.hamim.Mushaf;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.daftar_surat.DaftarsuratItem;
import co.metode.hamim.juz.CekDataHamim;
import co.metode.hamim.juz.JuzItem;
import co.metode.hamim.mushaf.mushafnew.MushafNew;
import co.metode.hamim.payment.Checkout;
import co.metode.hamim.qiblat.CompassActivity;
import co.metode.hamim.test.TestDownload;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class AdapterJuzCekBacaan extends RecyclerView.Adapter<AdapterJuzCekBacaan.AdapterHolder> {
    private Context context;
    private List<JuzItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    AdapterSuratCekBacaan adapterSuratCekBacaan;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterJuzCekBacaan(Context context, List<JuzItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }
    @NonNull
    @Override
    public AdapterJuzCekBacaan.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_juz_cek_bacaan,parent,false);
        AdapterJuzCekBacaan.AdapterHolder holder = new AdapterJuzCekBacaan.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterJuzCekBacaan.AdapterHolder holder, int position) {

        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        dialog = new Dialog(context);

        final JuzItem juzItem = dataList.get(position);
        String id_juz = juzItem.getIdJuz();
        String namaJuz = juzItem.getNamaJuz();
        String status = juzItem.getStatus();
        String url_gambar = juzItem.getUrl_gambar();
        String kode_program = juzItem.getKode_program();
        int diskon_poin = juzItem.getDiskon_poin();
        int harga = juzItem.getHarga();


        holder.namaJuzCekBacaan.setText(namaJuz);
        bottomSheetDialog = new BottomSheetDialog(context, R.style.AppBottomSheetDialogTheme);
        holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                push_dialog(namaJuz, id_juz, diskon_poin);
            }
        });
    }

    private void push_dialog(String namaJuz, String id_juz, int diskon_poin) {
        bottomSheetDialog.setContentView(R.layout.bottom_sheet_surat);

        RecyclerView RcListSuratCekBacaan = bottomSheetDialog.findViewById(R.id.RcListSuratCekBacaan);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context);
        RcListSuratCekBacaan.setLayoutManager(linearLayoutManager);

        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DaftarsuratItem>> call = apiInterface.getPos(id_juz);
        call.enqueue(new Callback<List<DaftarsuratItem>>() {
            @Override
            public void onResponse(Call<List<DaftarsuratItem>> call, Response<List<DaftarsuratItem>> response) {
                if (response.isSuccessful()){
                    List<DaftarsuratItem> list = response.body();
                    adapterSuratCekBacaan = new AdapterSuratCekBacaan(context, list, id_juz, diskon_poin);
                    RcListSuratCekBacaan.setAdapter(adapterSuratCekBacaan);
                }
            }

            @Override
            public void onFailure(Call<List<DaftarsuratItem>> call, Throwable t) {

            }
        });

        bottomSheetDialog.setCanceledOnTouchOutside(true);
        bottomSheetDialog.show();
    }

    @Override
    public int getItemCount() { return dataList.size(); }

    public class AdapterHolder extends RecyclerView.ViewHolder {

        TextView namaJuzCekBacaan;
        LinearLayout bodyJuzCekBacaan;
        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            namaJuzCekBacaan = itemView.findViewById(R.id.namaJuzCekBacaan);
            bodyJuzCekBacaan = itemView.findViewById(R.id.bodyJuzCekBacaan);

        }
    }
}
