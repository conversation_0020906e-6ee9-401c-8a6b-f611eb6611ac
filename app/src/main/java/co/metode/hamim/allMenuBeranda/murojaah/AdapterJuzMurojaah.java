package co.metode.hamim.allMenuBeranda.murojaah;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

import co.metode.hamim.MainActivity;
import co.metode.hamim.R;
import co.metode.hamim.SessionManager;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.juz.CekDataHamim;
import co.metode.hamim.juz.JuzItem;
import co.metode.hamim.paymentHamim.PayHamim;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterJuzMurojaah extends RecyclerView.Adapter<AdapterJuzMurojaah.AdapterHolder> {
    private Context context;
    private List<JuzItem> dataList;
    private Dialog dialog;
    ApiInterface apiInterface;
    private BottomSheetDialog bottomSheetDialog;

    public AdapterJuzMurojaah(Context context, List<JuzItem> dataList){
        this.context = context;
        this.dataList = dataList;
    }

    @NonNull
    @Override
    public AdapterJuzMurojaah.AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_juz_cek_bacaan,parent,false);
        AdapterJuzMurojaah.AdapterHolder holder = new AdapterJuzMurojaah.AdapterHolder(view);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterJuzMurojaah.AdapterHolder holder, int position) {
        SessionManager sessionManager = new SessionManager(context);
        String id_user = sessionManager.getUserDetail().get(SessionManager.ID_USER);

        dialog = new Dialog(context);

        final JuzItem juzItem = dataList.get(position);
        String id_juz = juzItem.getIdJuz();
        String namaJuz = juzItem.getNamaJuz();
        String url_gambar = juzItem.getUrl_gambar();
        String kode_murojaah = juzItem.getKode_murojaah();
        int harga_murojaah = juzItem.getHargaMurojaah();

        holder.namaJuzCekBacaan.setText(namaJuz);
        bottomSheetDialog = new BottomSheetDialog(context, R.style.AppBottomSheetDialogTheme);
        apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<CekDataHamim> cekDataHamimCall = apiInterface.cekDataHamim(id_user,kode_murojaah);
        cekDataHamimCall.enqueue(new Callback<CekDataHamim>() {
            @Override
            public void onResponse(Call<CekDataHamim> call, Response<CekDataHamim> response) {
                if (response.isSuccessful()){
                    if (response.body().getData().equals("1")){
                        if (response.body().getLimitKelas() == 1000){
                            holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Intent intent = new Intent(context, PilihanMurojaah.class);
                                    intent.putExtra("id_juz", id_juz);
                                    intent.putExtra("img", url_gambar);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                    context.startActivity(intent);
                                }
                            });
                        }else if(response.body().getLimitKelas() == 0){
                            holder.bodyJuzCekBacaan.setBackgroundResource(R.drawable.bg_point_lock);
                            holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    // Intent ke PayHamim dan kirim data
                                    Intent intent = new Intent(context, PayHamim.class);
                                    intent.putExtra("kode_program", kode_murojaah);
                                    intent.putExtra("nama_juz", "Murojaah " + namaJuz);
                                    intent.putExtra("harga", harga_murojaah);
                                    intent.putExtra("id_juz", id_juz);
                                    intent.putExtra("url_gambar", url_gambar);
                                    intent.putExtra("isMurojaahPayment", true);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                    context.startActivity(intent);
                                }
                            });
                            holder.lockHamim.setImageResource(R.drawable.ic_lock_new);
                        }else{
                            holder.lockHamim.setVisibility(View.GONE);
                            holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Intent intent = new Intent(context, PilihanMurojaah.class);
                                    intent.putExtra("id_juz", id_juz);
                                    intent.putExtra("img", url_gambar);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                    context.startActivity(intent);
                                }
                            });
                            String limit = String.valueOf(response.body().getLimitKelas());
                            holder.limitHamim.setText(limit);
                            holder.layoutLimitHamim.setVisibility(View.VISIBLE);
                        }
                    }else{
                        //jika tidak ada id user
                        holder.bodyJuzCekBacaan.setBackgroundResource(R.drawable.bg_point_lock);
                        if(id_user.equals("")){
                            //pindah kelogin
                            holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Intent intent = new Intent(context, MainActivity.class);
                                    context.startActivity(intent);
                                    ((Activity)context).finish();
                                }
                            });
                            holder.lockHamim.setImageResource(R.drawable.ic_lock_new);
                        }else {
                            holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    // Intent ke PayHamim dan kirim data
                                    Intent intent = new Intent(context, PayHamim.class);
                                    intent.putExtra("kode_program", kode_murojaah);
                                    intent.putExtra("nama_juz", "Murojaah " + namaJuz);
                                    intent.putExtra("harga", harga_murojaah);
                                    intent.putExtra("id_juz", id_juz);
                                    intent.putExtra("url_gambar", url_gambar);
                                    intent.putExtra("isMurojaahPayment", true); // Tandai bahwa ini pembayaran Murojaah
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                    context.startActivity(intent);
                                }
                            });
                            holder.lockHamim.setImageResource(R.drawable.ic_lock_new);
                        }

                    }
                }else {
                    holder.bodyJuzCekBacaan.setBackgroundResource(R.drawable.bg_point_lock);
                    holder.bodyJuzCekBacaan.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            // Intent ke PayHamim dan kirim data
                            Intent intent = new Intent(context, PayHamim.class);
                            intent.putExtra("kode_program", kode_murojaah);
                            intent.putExtra("nama_juz", "Murojaah " + namaJuz);
                            intent.putExtra("harga", harga_murojaah);
                            intent.putExtra("id_juz", id_juz);
                            intent.putExtra("url_gambar", url_gambar);
                            intent.putExtra("isMurojaahPayment", true); // Tandai bahwa ini pembayaran Murojaah
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            context.startActivity(intent);
                        }
                    });
                    holder.lockHamim.setImageResource(R.drawable.ic_lock_new);
                    Toast.makeText(context, "Terjadi Kesalahan", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<CekDataHamim> call, Throwable t) {
                Toast.makeText(context, "Periksa Internet", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class AdapterHolder extends RecyclerView.ViewHolder {
        TextView namaJuzCekBacaan,limitHamim;
        ImageView lockHamim;
        LinearLayout bodyJuzCekBacaan,layoutLimitHamim;

        public AdapterHolder(@NonNull View itemView) {
            super(itemView);

            namaJuzCekBacaan = itemView.findViewById(R.id.namaJuzCekBacaan);
            bodyJuzCekBacaan = itemView.findViewById(R.id.bodyJuzCekBacaan);
            lockHamim = itemView.findViewById(R.id.imageJuzCekBacaan);
            layoutLimitHamim = itemView.findViewById(R.id.layoutLimitHamim);
            limitHamim = itemView.findViewById(R.id.limitHamim);
        }
    }
}