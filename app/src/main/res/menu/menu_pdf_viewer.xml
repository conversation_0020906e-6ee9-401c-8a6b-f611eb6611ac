<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- Night Mode Toggle -->
    <item
        android:id="@+id/action_night_mode"
        android:icon="@drawable/ic_brightness_6"
        android:title="@string/night_mode"
        app:showAsAction="ifRoom" />

    <!-- Zoom In -->
    <item
        android:id="@+id/action_zoom_in"
        android:icon="@drawable/ic_zoom_in"
        android:title="@string/zoom_in"
        app:showAsAction="ifRoom" />

    <!-- Zoom Out -->
    <item
        android:id="@+id/action_zoom_out"
        android:icon="@drawable/ic_zoom_out"
        android:title="@string/zoom_out"
        app:showAsAction="ifRoom" />

    <!-- Fit to Width -->
    <item
        android:id="@+id/action_fit_width"
        android:icon="@drawable/ic_fit_screen"
        android:title="@string/fit_to_width"
        app:showAsAction="never" />

    <!-- Share -->
    <item
        android:id="@+id/action_share"
        android:icon="@drawable/ic_share"
        android:title="@string/share"
        app:showAsAction="never" />

    <!-- Open with External App -->
    <item
        android:id="@+id/action_open_external"
        android:icon="@drawable/ic_open_in_new"
        android:title="@string/open_with_external_app"
        app:showAsAction="never" />

    <!-- Page Navigation -->
    <item
        android:id="@+id/action_go_to_page"
        android:icon="@drawable/ic_format_list_numbered"
        android:title="@string/go_to_page"
        app:showAsAction="never" />

    <!-- Reading Mode -->
    <item
        android:id="@+id/action_reading_mode"
        android:title="@string/reading_mode"
        app:showAsAction="never">
        <menu>
            <item
                android:id="@+id/action_vertical_scroll"
                android:title="@string/vertical_scroll"
                android:checkable="true"
                android:checked="true" />
            <item
                android:id="@+id/action_horizontal_scroll"
                android:title="@string/horizontal_scroll"
                android:checkable="true" />
        </menu>
    </item>

    <!-- View Options -->
    <item
        android:id="@+id/action_view_options"
        android:title="@string/view_options"
        app:showAsAction="never">
        <menu>
            <item
                android:id="@+id/action_fit_width"
                android:title="@string/fit_to_width"
                android:checkable="true"
                android:checked="true" />
            <item
                android:id="@+id/action_fit_height"
                android:title="@string/fit_to_height"
                android:checkable="true" />
            <item
                android:id="@+id/action_fit_both"
                android:title="@string/fit_both"
                android:checkable="true" />
        </menu>
    </item>

</menu>
