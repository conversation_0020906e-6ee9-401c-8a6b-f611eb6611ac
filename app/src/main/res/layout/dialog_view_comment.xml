<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvHighlightedText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="#FFFFCC"
        android:padding="8dp"
        android:textColor="@android:color/black"
        android:textStyle="italic" />

    <TextView
        android:id="@+id/tvCommentLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Comment:"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvComment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@android:color/black" />

</LinearLayout>
